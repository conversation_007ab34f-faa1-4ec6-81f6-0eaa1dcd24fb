<!DOCTYPE html>
<html>
<head>
    <title>Test Dynamic Form Component</title>
</head>
<body>
    <h1>Test để kiểm tra DynamicFormFields Component</h1>
    
    <h2><PERSON><PERSON><PERSON> bước kiểm tra:</h2>
    <ol>
        <li>Mở JobAdd.vue trong browser</li>
        <li>Chọn một workflow từ dropdown</li>
        <li>Kiểm tra xem các field động có hiển thị không</li>
        <li>Kiểm tra validation có hoạt động không</li>
        <li>Kiểm tra submit form có hoạt động không</li>
    </ol>
    
    <h2>Các vấn đề có thể gặp:</h2>
    <ul>
        <li><strong>Fields không hiển thị:</strong> Kiểm tra console để xem có lỗi API không</li>
        <li><strong>Component không render:</strong> Kiểm tra formId có được truyền đúng không</li>
        <li><strong>Validation không hoạt động:</strong> Kiểm tra schema có được setup đúng không</li>
        <li><strong>Data không được submit:</strong> Kiểm tra event handlers có hoạt động không</li>
    </ul>
    
    <h2>Debug steps:</h2>
    <pre>
// Trong browser console, kiểm tra:
console.log('FormId:', this.state.formDataJob.workflow.formId);
console.log('Dynamic Form Fields:', this.$refs.dynamicFormFields);
console.log('Form Data:', this.state.formData);
    </pre>
</body>
</html>
