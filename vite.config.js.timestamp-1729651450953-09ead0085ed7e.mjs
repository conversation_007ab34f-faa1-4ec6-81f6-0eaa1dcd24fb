// vite.config.js
import { defineConfig } from "file:///C:/xampp/htdocs/tvnas-app/node_modules/vite/dist/node/index.js";
import laravel from "file:///C:/xampp/htdocs/tvnas-app/node_modules/laravel-vite-plugin/dist/index.js";
import vue from "file:///C:/xampp/htdocs/tvnas-app/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import autoprefixer from "file:///C:/xampp/htdocs/tvnas-app/node_modules/autoprefixer/lib/autoprefixer.js";
var __vite_injected_original_dirname = "C:\\xampp\\htdocs\\tvnas-app";
var vite_config_default = defineConfig(() => {
  return {
    plugins: [
      laravel({
        input: [
          "resources/js/app.ts"
        ],
        refresh: true
      }),
      vue({
        template: {
          transformAssetUrls: {
            base: null,
            includeAbsolute: false
          }
        }
      })
    ],
    css: {
      postcss: {
        plugins: [
          autoprefixer({})
          // add options if needed
        ]
      },
      preprocessorOptions: {
        scss: {
          quietDeps: true
        }
      }
    },
    resolve: {
      alias: {
        vue: "vue/dist/vue.esm-bundler.js",
        "@": path.resolve(__vite_injected_original_dirname, "resources/js")
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue", ".scss"]
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
