<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('process_version', function (Blueprint $table) {
            $table->uuid('id')->primary();  // UUID làm khóa chính
            $table->uuid('process_id');  // Thuộc quy trình
            $table->integer('version_number'); // Số phiên bản của quy trình
            $table->json('change_log');  // <PERSON><PERSON> tả những thay đổi
            $table->boolean('is_active')->default(true)->comment('0 - Không hoạt động, 1 - Hoạt động');  // Trạng thái hoạt động
            $table->uuid('create_by');  // Người tạo
            $table->timestamps();
        });

        // Tạo chỉ mục
        Schema::table('process_version', function (Blueprint $table) {
            $table->index(['id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('process_version');
    }
};
