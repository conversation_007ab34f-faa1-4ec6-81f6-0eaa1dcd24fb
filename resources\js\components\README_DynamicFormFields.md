# DynamicFormFields Component

## Mô tả
Component `DynamicFormFields.vue` là một component dùng chung được tách ra từ `JobAdd.vue` để hiển thị và xử lý các form động. Component này chứa toàn bộ logic xử lý các loại field khác nhau như MULTISELECT, USER, DEPARTMENT, FILEUPLOAD, FORMULA, TABLE, OBJECTSYSTEM, v.v.

## Tính năng chính
- Hiển thị các field động dựa trên cấu hình từ API
- Hỗ trợ nhiều loại field: text, textarea, number, date, multiselect, user, department, file upload, formula, table
- Validation tự động cho các field required
- Xử lý table với các field con phức tạp
- Tính toán formula tự động
- Tích hợp với VeeValidate và Yup cho validation

## Props
- `formId`: ID của form để load các field động
- `initialFormData`: Dữ liệu khởi tạo cho form

## Events
- `submit`: Emit khi form được submit với dữ liệu form
- `update:formData`: Emit khi dữ liệu form thay đổi

## Cách sử dụng

### 1. Import component
```vue
import DynamicFormFields from '@/components/DynamicFormFields.vue'
```

### 2. Đăng ký component
```vue
components: {
  DynamicFormFields
}
```

### 3. Sử dụng trong template
```vue
<DynamicFormFields
  :formId="workflowFormId"
  :initialFormData="formData"
  @submit="handleDynamicFormSubmit"
  @update:formData="updateFormData"
  ref="dynamicFormFields"
/>
```

### 4. Xử lý events
```javascript
const handleDynamicFormSubmit = (formData) => {
  // Xử lý dữ liệu form khi submit
  console.log('Form data:', formData)
}

const updateFormData = (formData) => {
  // Cập nhật dữ liệu form khi thay đổi
  state.formData = formData
}
```

## Cấu trúc dữ liệu trả về
```javascript
{
  formData: {}, // Dữ liệu các field chính
  itemChildrens: {}, // Dữ liệu các field trong table
  dataFormFields: [] // Cấu hình các field
}
```

## Ví dụ trong JobAdd.vue
Component `JobAdd.vue` đã được cập nhật để sử dụng `DynamicFormFields`:

```vue
<template>
  <!-- Các field cố định của Job -->
  <FormKit type="text" v-model="formDataJob.name" />
  <Multiselect v-model="formDataJob.workflow" />
  
  <!-- Component form động -->
  <DynamicFormFields
    :formId="formDataJob.workflow.formId"
    @submit="handleDynamicFormSubmit"
    ref="dynamicFormFields"
  />
</template>
```

## Lưu ý
- Component này hoàn toàn tự chứa với tất cả logic validation
- Có thể tái sử dụng trong nhiều component khác nhau
- Dữ liệu form được quản lý độc lập trong component
- Cần truyền `formId` để component tự động load các field từ API
