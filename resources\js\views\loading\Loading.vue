<template>
    <vue-element-loading
        :active="isLoading"
        spinner="mini-spinner"
        color="#FF6700"
        size="50"
        duration="1.0"
        is-full-screen
    />
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import VueElementLoading from 'vue-element-loading';

export default defineComponent({
    name: 'Loading',

    props: {
        isLoading: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        VueElementLoading
    }
})
</script>
