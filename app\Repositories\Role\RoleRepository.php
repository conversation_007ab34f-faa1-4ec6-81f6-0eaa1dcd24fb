<?php
namespace App\Repositories\Role;

use App\Repositories\EloquentRepository;
use App\Enums\RoleStatus;
use App\Enums\UserStatus;
use App\Enums\RoleIsHidden;

class RoleRepository extends EloquentRepository implements RoleRepositoryInterface
{
	public function getModel()
	{
		return \App\Models\Role::class;
	}

    public function getAllRoleOptions()
    {
        $select_columns = [
			'id',
			'name',	
			'is_hidden',
		];

        $query = $this->model->select($select_columns);

        // Ki<PERSON>m tra quyền SUPPER-ADMIN-SETTING
        $user = auth()->user();
        $hasSuperAdminPermission = $user->roles()
            ->whereHas('permissions', function($q) {
                $q->where('slug', 'SUPPER-ADMIN-SETTING');
            })->exists();

        // Nếu không có quyền SUPPER-ADMIN-SETTING, chỉ lấy các role có is_hidden = 1
        if (!$hasSuperAdminPermission) {
            $query->where('is_hidden', RoleIsHidden::SHOW->value);
        }

        return $query->get();
    }

	public function getAllRoles($dataSearch)
	{
        $page = $dataSearch['page'] ?? null;
        $perPage = $dataSearch['perPage'] ?? null;
		
		$select_columns = [
			'id',
			'name',	
			'is_hidden',
			'create_by',
            'update_by',
            'created_at',
            'updated_at',
            'expired_at',
		];
		
		$query = $this->model
			->with(['createBy:id,full_name', 'updateBy:id,full_name'])
			->select($select_columns);

        // Kiểm tra quyền SUPPER-ADMIN-SETTING
        $user = auth()->user();
        $hasSuperAdminPermission = $user->roles()
            ->whereHas('permissions', function($q) {
                $q->where('slug', 'SUPPER-ADMIN-SETTING');
            })->exists();

        // Nếu không có quyền SUPPER-ADMIN-SETTING, chỉ lấy các role có is_hidden = 1
        if (!$hasSuperAdminPermission) {
            $query->where('is_hidden', RoleIsHidden::SHOW->value);
        }

        // Tạo baseQuery để sử dụng cho việc đếm các tab
        $baseQuery = clone $query;
        
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';
        $query->orderBy($orderBy, $orderDirection);
        $statusList = [
            RoleStatus::ALL->name
        ];

        $counts = [];
        foreach ($statusList as $status) {
            $countQuery = clone $baseQuery;
            $counts[strtolower($status)] = $countQuery->count();
        }

        // Lấy danh sách vai trò với phân trang hoặc tất cả
        if ($page && $perPage) {
            $roles = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $roles = $query->get();
        }
        
        // Lấy thông tin về quyền ADMIN-SETTING và số lượng người dùng
        $rolesWithDetails = $roles->map(function($role) {
            // Sử dụng eager loading để lấy thông tin quyền và người dùng
            $role->load([
                'permissions' => function($query) {
                    $query->where('slug', 'ADMIN-SETTING')->select('permissions.id', 'permissions.slug');
                },
                'users' => function($query) {
                    $query->select('users.id', 'users.is_active');
                }
            ]);
            
            // Kiểm tra xem vai trò có quyền ADMIN-SETTING không
            $role->is_admin = $role->permissions->isNotEmpty();
            
            // Đếm số lượng người dùng đang hoạt động và không hoạt động
            $role->active_users_count = $role->users->where('is_active', UserStatus::ACTIVE->value)->count();
            $role->inactive_users_count = $role->users->where('is_active', UserStatus::UNACTIVE->value)->count();
            
            // Lấy danh sách người dùng đang hoạt động và không hoạt động
            $role->active_users = $role->users->where('is_active', UserStatus::ACTIVE->value)->values();
            $role->inactive_users = $role->users->where('is_active', UserStatus::UNACTIVE->value)->values();
            
            // Xóa thông tin không cần thiết để giảm kích thước dữ liệu trả về
            unset($role->permissions);
            unset($role->users);
            
            return $role;
        });
        
        // Nếu đang sử dụng phân trang, cập nhật collection trong đối tượng phân trang
        if ($page && $perPage) {
            $roles->setCollection($rolesWithDetails);
            return [
                'roles' => $roles,
                'counts' => $counts
            ];
        }
        
        return [
            'roles' => $rolesWithDetails,
            'counts' => $counts
        ];
	}
}
?>