<template>
    <CCol :xs="12">
        <BTabs no-body content-class="mt-3" v-model="state.tabIndex">
            <BTab :title="$t('workflow.condition.create')" active>
                <Form ref="form" @submit="handleSubmitFormCondition" :validation-schema="schema()">
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.condition.name') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataCondition.name"
                            name="name" 
                            type="text" 
                            class="form-control" 
                            maxlength="200" 
                            @change="handleInputNameAdd(dataCondition.name)"
                        />
                        <ErrorMessage
                            as="div"
                            name="name"
                            class="text-danger"
                        />
                    </CCol>
                    <CCol :xs="12" class="mb-3">
                        <label class="mb-1">
                            {{ $t('workflow.condition.slug') }}
                            <span class="text-danger">*</span>
                        </label>
                        <Field 
                            v-model="dataCondition.slug"
                            name="slug" 
                            type="text" 
                            class="form-control" 
                            maxlength="200" 
                            :readonly="true"
                        />
                        <ErrorMessage
                            as="div"
                            name="slug"
                            class="text-danger"
                        />
                        <div class="text-danger mb-3" v-if="checkDuplicateSlugAdd(dataCondition.slug)">
                            {{ $t('workflow.condition.validate.duplicate_slug') }}
                        </div>
                    </CCol>
                    <div v-for="(condition, index) in dataCondition.arrayConditions" :key="index">
                        <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab mb-3" v-if="index > 0">
                            <template #button-content>
                                <span class="text-secondary cursor-pointer bg-select-logical-operator">{{ getLogicalOperatorLabel(condition.logicalOperator) }}</span>
                            </template>
                            <b-dropdown-item 
                                v-for="(optionLogicalOperator, indexLogicalOperator) in state.selectOptionLogicalOperators"
                                :key="indexLogicalOperator" 
                                @click="selectLogicalOperator(index, optionLogicalOperator.value)"
                            >
                                {{ optionLogicalOperator.label }}
                            </b-dropdown-item>
                        </b-dropdown>
                        <div class="filter-container mb-3 border">
                            <div class="row">
                                <div class="col-md-4">
                                    <label class="form-label required-label">
                                        {{ $t('workflow.condition.object') }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="selection-field">
                                        <Field 
                                            :name="`conditions[${index}].object`"
                                            as="select" 
                                            class="form-select" 
                                            v-model="condition.object" 
                                            @change="handleChangeObject(index)"
                                        >
                                            <option v-for="(optionObject, indexOptionObject) in state.selectOptionObjects" :value="optionObject.value" :key="indexOptionObject">
                                                {{ optionObject.label }}
                                            </option>
                                        </Field>
                                        <ErrorMessage
                                            as="div"
                                            :name="`conditions[${index}].object`"
                                            class="text-danger"
                                        />
                                    </div>
                                </div>
                                <div class="col-md-7">
                                    <label class="form-label required-label">
                                        {{ $t('workflow.condition.add') }}
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="condition-container">
                                        <div class="mb-2">
                                            <i>{{ $t('workflow.condition.and') }}</i>
                                        </div>
                                        <div v-for="(conditionSub, subIndex) in dataCondition.arrayConditionSubs[index]" :key="subIndex">
                                            <div class="d-flex align-items-center">
                                                <span class="field-label">{{ conditionSub.field.display_name }}</span>
                                                <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab dropdown-operator">
                                                    <template #button-content>
                                                        <span class="ms-3 text-secondary cursor-pointer">{{ getOperatorLabel(conditionSub) }}</span>
                                                    </template>
                                                    <b-dropdown-item 
                                                        v-for="(optionOperator, indexOperator) in conditionSub.filteredOperators"
                                                        :key="indexOperator" 
                                                        @click="selectOperator(index, subIndex, optionOperator)"
                                                    >
                                                        {{ optionOperator.label }}
                                                    </b-dropdown-item>
                                                </b-dropdown>
                                            </div>
                                            <div class="date-input-container d-flex align-items-center">
                                                <!-- {{ conditionSub.field }} -->
                                                <input 
                                                    type="text" 
                                                    class="form-control"
                                                    v-model="conditionSub.value"
                                                    v-if="checkInputTypeText(conditionSub.field.type)" 
                                                >
                                                <div class="d-flex align-items-center" v-if="conditionSub.operator.value === 'between' && checkInputTypeNumber(conditionSub.field.type)">
                                                    <input 
                                                        type="number" 
                                                        class="form-control me-4"
                                                        v-model="conditionSub.value[0]" 
                                                    >
                                                    <input 
                                                        type="number" 
                                                        class="form-control"
                                                        v-model="conditionSub.value[1]" 
                                                    >
                                                </div>
                                                <input 
                                                    type="number" 
                                                    class="form-control" 
                                                    v-model="conditionSub.value"
                                                    :disabled="conditionSub.disableInput"
                                                    v-if="conditionSub.operator.value !== 'between' && checkInputTypeNumber(conditionSub.field.type)"
                                                >
                                                <input 
                                                    type="time" 
                                                    class="form-control" 
                                                    v-model="conditionSub.value"
                                                    v-if="checkInputTypeTime(conditionSub.field.type)" 
                                                >
                                                <div class="d-flex align-items-center" v-if="conditionSub.operator.value === 'between' && checkInputTypeDate(conditionSub.field.type)">
                                                    <input 
                                                        type="date" 
                                                        class="form-control me-4"
                                                        v-model="conditionSub.value[0]" 
                                                    >
                                                    <input 
                                                        type="date" 
                                                        class="form-control"
                                                        v-model="conditionSub.value[1]" 
                                                    >
                                                </div>
                                                <input 
                                                    type="date" 
                                                    class="form-control" 
                                                    v-model="conditionSub.value"
                                                    :disabled="conditionSub.disableInput"
                                                    v-if="conditionSub.operator.value !== 'between' && checkInputTypeDate(conditionSub.field.type)"
                                                >
                                                <Multiselect
                                                    v-model="conditionSub.value"
													:mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                    :placeholder="$t('workflow.choose')"
													:close-on-select="false"
													:searchable="true"
													:options="conditionSub.field.options"
                                                    :can-clear="false"
                                                    v-if="checkInputTypeSelect(conditionSub.field.type)"
												/>
                                                <Multiselect
                                                    v-model="conditionSub.value"
													:mode="conditionSub.field.multiple ? 'tags' : 'single'"
													:placeholder="$t('workflow.choose')"
													:close-on-select="false"
													:searchable="true"
													:object="true"
													:options="state.selectOptionDepartments"
													:can-clear="false"
                                                    v-if="checkInputTypeDepartment(conditionSub.field.type)"
												>
                                                    <template v-slot:option="{ option }">
                                                        <div class="custom-option">
                                                            <div class="option-label mb-1">
                                                                {{ option.label }}
                                                            </div>
                                                            <div class="option-description text-secondary">
                                                                <small>
                                                                    <i>{{ option.type }}</i>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </Multiselect>
                                                <Multiselect
                                                    v-model="conditionSub.value"
                                                    :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                    :placeholder="$t('workflow.choose')"
                                                    :close-on-select="false"
                                                    :filter-results="false"
                                                    :resolve-on-load="false"
                                                    :infinite="true"
                                                    :limit="10"
                                                    :clear-on-search="true"
                                                    :searchable="true"
                                                    :delay="0"
                                                    :min-chars="0"
                                                    :object="true"
                                                    :options="async (query) => {
                                                        return await debouncedGetOptionUsers(query)
                                                    }"
                                                    :can-clear="false"
                                                    @open="debouncedGetOptionUsers('')"
                                                    v-if="checkInputTypeUser(conditionSub.field.type)"
                                                />
                                                <Multiselect
                                                    v-model="conditionSub.value"
                                                    :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                    :placeholder="$t('workflow.choose')"
                                                    :close-on-select="false"
                                                    :filter-results="false"
                                                    :resolve-on-load="false"
                                                    :infinite="true"
                                                    :limit="10"
                                                    :clear-on-search="true"
                                                    :searchable="true"
                                                    :delay="0"
                                                    :min-chars="0"
                                                    :object="true"
                                                    :options="async (query) => {
                                                        return await debouncedGetOptionColumnData(query, conditionSub.field)
                                                    }"
                                                    :can-clear="false"
                                                    @open="debouncedGetOptionColumnData('', conditionSub.field)"
                                                    v-if="checkInputTypeObjectSystem(conditionSub.field.type)"
                                                />
                                                <Multiselect
                                                    v-model="conditionSub.value"
													mode="tags"
													:placeholder="$t('workflow.choose')"
													:close-on-select="false"
													:searchable="true"
													:object="true"
													:options="state.selectOptionDepartments"
													:can-clear="false"
                                                    v-if="checkInputTypeDepartmentSystem(conditionSub.field.type)"
												>
                                                    <template v-slot:option="{ option }">
                                                        <div class="custom-option">
                                                            <div class="option-label mb-1">
                                                                {{ option.label }}
                                                            </div>
                                                            <div class="option-description text-secondary">
                                                                <small>
                                                                    <i>{{ option.type }}</i>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </template>
                                                </Multiselect>
                                                <Multiselect
                                                    v-model="conditionSub.value"
													mode="tags"
													:placeholder="$t('workflow.choose')"
													:close-on-select="false"
													:searchable="true"
													:object="true"
													:options="state.selectOptionJobPositionSystems"
													:can-clear="false"
                                                    v-if="checkInputTypeJobPositionSystem(conditionSub.field.type)"
												/>
                                                <Multiselect
                                                    v-model="conditionSub.value"
													mode="tags"
													:placeholder="$t('workflow.choose')"
													:close-on-select="false"
													:searchable="true"
													:object="true"
													:options="state.selectOptionRanks"
													:can-clear="false"
                                                    v-if="checkInputTypeRank(conditionSub.field.type)"
												/>
                                                <Multiselect
                                                    v-model="conditionSub.value"
                                                    mode="tags"
                                                    :placeholder="$t('workflow.choose')"
                                                    :close-on-select="false"
                                                    :filter-results="false"
                                                    :resolve-on-load="false"
                                                    :infinite="true"
                                                    :limit="10"
                                                    :clear-on-search="true"
                                                    :searchable="true"
                                                    :delay="0"
                                                    :min-chars="0"
                                                    :object="true"
                                                    :options="async (query) => {
                                                        return await debouncedGetOptionUsers(query)
                                                    }"
                                                    :can-clear="false"
                                                    @open="debouncedGetOptionUsers('')"
                                                    v-if="checkInputTypeCreateBy(conditionSub.field.type, conditionSub.field.object)"
                                                />
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    height="24px"
                                                    viewBox="0 -960 960 960"
                                                    width="24px"
                                                    fill="#83868C"
                                                    class="cursor-pointer ms-2 float-end"
                                                    @click="removeConditionSub(index, subIndex)" 
                                                >
                                                    <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
                                                </svg>
                                            </div>
                                        </div>
                                        <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab">
                                            <template #button-content>
                                                <div class="d-flex align-items-center p-1">
                                                    <div 
                                                        class="d-flex align-items-center bg-add-condition cursor-pointer" 
                                                        :title="$t('workflow.condition.add')" 
                                                        v-b-tooltip.hover 
                                                        @click="addConditionSub(index)"
                                                    >
                                                        <span class="material-symbols-outlined">add_circle</span>
                                                        <span class="ms-1 text-secondary">{{ $t('workflow.condition.add') }}</span>
                                                    </div>
                                                </div>
                                            </template>
                                            <div class="px-3 py-2 search-container" @click.stop>
                                                <BInputGroup>
                                                    <template #append>
                                                        <BInputGroupText>
                                                            <span class="material-icons-outlined">search</span>
                                                        </BInputGroupText>
                                                    </template>
                                                    <BFormInput :placeholder="$t('search.title')" v-model="state.searchQueryField"/>
                                                </BInputGroup>
                                            </div>
                                            <b-dropdown-divider></b-dropdown-divider>
                                            <b-dropdown-item 
                                                v-for="(field, indexField) in filteredOptionFields" 
                                                :key="indexField" 
                                                @click="selectField(index, field)"
                                            >
                                                {{ field.display_name }}
                                            </b-dropdown-item>
                                            <div v-if="filteredOptionFields.length === 0" class="px-3 py-2">
                                                {{ $t('search.no_matching_records_found') }}
                                            </div>
                                        </b-dropdown>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        height="24px"
                                        viewBox="0 -960 960 960"
                                        width="24px"
                                        fill="#83868C"
                                        class="cursor-pointer ms-2"
                                        @click="removeCondition(index)" 
                                    >
                                        <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button 
                        type="button" 
                        class="btn btn-primary mb-3 d-flex align-items-center" 
                        @click="addCondition()"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
                            <path d="M440-280h80v-160h160v-80H520v-160h-80v160H280v80h160v160Zm40 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/>
                        </svg>
                    </button>
                    <CCardFooter>
                        <div class="d-flex justify-content-end">
                            <CButton 
                                type="button"
                                class="btn btn-light border m-1"
                                @click="closeFormCondition"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.condition.close') }}
                                </span>
                            </CButton>
                            <CButton 
                                type="submit"
                                class="btn btn-primary m-1"
                            >
                                <span class="text-uppercase">
                                    {{ $t('workflow.condition.save_update') }}
                                </span>
                            </CButton>
                        </div>
                    </CCardFooter>
                </Form>
            </BTab>
            <BTab :title="$t('workflow.condition.list')">
                <CTable align="middle" responsive>
                    <table class="table table-hover">
                        <tbody v-if="listDataConditions.length > 0">
                            <tr 
                                v-for="(condition, index) in listDataConditions" 
                                :key="index"
                            >
                                <td class="align-middle">{{ condition.value.name }}</td> 
                                <td class="align-middle col-sm-1 table__td--action">
                                    <svg @click="editConditionList(index, condition.value)"  class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M14.06 9.02l.92.92L5.92 19H5v-.92l9.06-9.06M17.66 3c-.25 0-.51.1-.7.29l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.2-.2-.45-.29-.71-.29zm-3.6 3.19L3 17.25V21h3.75L17.81 9.94l-3.75-3.75z"/>
                                    </svg>    
                                    <svg @click="removeConditionList(index)" class="me-2" xmlns="http://www.w3.org/2000/svg" height="18px" viewBox="0 0 24 24" width="18px" fill="#83868C">
                                        <path d="M0 0h24v24H0V0z" fill="none"/>
                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                                    </svg>  
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr>
                                <td colspan="2" class="align-middle text-center">
                                    {{ $t('search.no_matching_records_found') }}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </CTable>
                <BAccordion v-if="state.activeTabEdit">
                    <BAccordionItem :title="$t('workflow.condition.edit')" visible>
                        <Form ref="form" @submit="handleSubmitEditCondition" :validation-schema="schema()">
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.condition.name') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.conditionDetail.name"
                                    name="name" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="200" 
                                    @change="handleInputNameEdit(state.conditionDetail.name)"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="name"
                                    class="text-danger"
                                />
                            </CCol>
                            <CCol :xs="12" class="mb-3">
                                <label class="mb-1">
                                    {{ $t('workflow.condition.slug') }}
                                    <span class="text-danger">*</span>
                                </label>
                                <Field 
                                    v-model="state.conditionDetail.slug"
                                    name="slug" 
                                    type="text" 
                                    class="form-control" 
                                    maxlength="200" 
                                    :readonly="true"
                                />
                                <ErrorMessage
                                    as="div"
                                    name="slug"
                                    class="text-danger"
                                />
                                <div class="text-danger mb-3" v-if="checkDuplicateSlugEdit(state.conditionDetail.slug, state.indexEdit)">
                                    {{ $t('workflow.condition.validate.duplicate_slug') }}
                                </div>
                            </CCol>
                            <div v-for="(condition, index) in state.conditionDetail.arrayConditions" :key="index">
                                <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab mb-3" v-if="index > 0">
                                    <template #button-content>
                                        <span class="text-secondary cursor-pointer bg-select-logical-operator">{{ getLogicalOperatorLabel(condition.logicalOperator) }}</span>
                                    </template>
                                    <b-dropdown-item 
                                        v-for="(optionLogicalOperator, indexLogicalOperator) in state.selectOptionLogicalOperators"
                                        :key="indexLogicalOperator" 
                                        @click="selectLogicalOperator(index, optionLogicalOperator.value)"
                                    >
                                        {{ optionLogicalOperator.label }}
                                    </b-dropdown-item>
                                </b-dropdown>
                                <div class="filter-container mb-3 border">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label required-label">
                                                {{ $t('workflow.condition.object') }}
                                                <span class="text-danger">*</span>
                                            </label>
                                            <div class="selection-field">
                                                <Field 
                                                    :name="`conditions[${index}].object`"
                                                    as="select" 
                                                    class="form-select" 
                                                    v-model="condition.object" 
                                                >
                                                    <option v-for="(optionObject, indexOptionObject) in state.selectOptionObjects" :value="optionObject.value" :key="indexOptionObject">
                                                        {{ optionObject.label }}
                                                    </option>
                                                </Field>
                                                <ErrorMessage
                                                    as="div"
                                                    :name="`conditions[${index}].object`"
                                                    class="text-danger"
                                                />
                                            </div>
                                        </div>
                                        <div class="col-md-7">
                                            <label class="form-label required-label">
                                                {{ $t('workflow.condition.add') }}
                                                <span class="text-danger">*</span>
                                            </label>
                                            <div class="condition-container">
                                                <div class="mb-2">
                                                    <i>{{ $t('workflow.condition.and') }}</i>
                                                </div>
                                                <div v-for="(conditionSub, subIndex) in state.conditionDetail.arrayConditionSubs[index]" :key="subIndex">
                                                    <div class="d-flex align-items-center">
                                                        <span class="field-label">{{ conditionSub.field.display_name }}</span>
                                                        <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab dropdown-operator">
                                                            <template #button-content>
                                                                <span class="ms-3 text-secondary cursor-pointer">{{ getOperatorLabel(conditionSub) }}</span>
                                                            </template>
                                                            <b-dropdown-item 
                                                                v-for="(optionOperator, indexOperator) in conditionSub.filteredOperators"
                                                                :key="indexOperator" 
                                                                @click="selectOperatorEdit(index, subIndex, optionOperator)"
                                                            >
                                                                {{ optionOperator.label }}
                                                            </b-dropdown-item>
                                                        </b-dropdown>
                                                    </div>
                                                    <div class="date-input-container d-flex align-items-center">
                                                        <input 
                                                            type="text" 
                                                            class="form-control"
                                                            v-model="conditionSub.value"
                                                            v-if="checkInputTypeText(conditionSub.field.type)" 
                                                        >
                                                        <div class="d-flex align-items-center" v-if="conditionSub.operator.value === 'between' && checkInputTypeNumber(conditionSub.field.type)">
                                                            <input 
                                                                type="number" 
                                                                class="form-control me-4"
                                                                v-model="conditionSub.value[0]" 
                                                            >
                                                            <input 
                                                                type="number" 
                                                                class="form-control"
                                                                v-model="conditionSub.value[1]" 
                                                            >
                                                        </div>
                                                        <input 
                                                            type="number" 
                                                            class="form-control" 
                                                            v-model="conditionSub.value"
                                                            :disabled="conditionSub.disableInput"
                                                            v-if="conditionSub.operator.value !== 'between' && checkInputTypeNumber(conditionSub.field.type)"
                                                        >
                                                        <input 
                                                            type="time" 
                                                            class="form-control" 
                                                            v-model="conditionSub.value"
                                                            v-if="checkInputTypeTime(conditionSub.field.type)" 
                                                        >
                                                        <div class="d-flex align-items-center" v-if="conditionSub.operator.value === 'between' && checkInputTypeDate(conditionSub.field.type)">
                                                            <input 
                                                                type="date" 
                                                                class="form-control me-4"
                                                                v-model="conditionSub.value[0]" 
                                                            >
                                                            <input 
                                                                type="date" 
                                                                class="form-control"
                                                                v-model="conditionSub.value[1]" 
                                                            >
                                                        </div>
                                                        <input 
                                                            type="date" 
                                                            class="form-control" 
                                                            v-model="conditionSub.value"
                                                            :disabled="conditionSub.disableInput"
                                                            v-if="conditionSub.operator.value !== 'between' && checkInputTypeDate(conditionSub.field.type)"
                                                        >
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :options="conditionSub.field.options"
                                                            :can-clear="false"
                                                            v-if="checkInputTypeSelect(conditionSub.field.type)"
                                                        />
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :object="true"
                                                            :options="state.selectOptionDepartments"
                                                            :can-clear="false"
                                                            v-if="checkInputTypeDepartment(conditionSub.field.type)"
                                                        />
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :filter-results="false"
                                                            :resolve-on-load="false"
                                                            :infinite="true"
                                                            :limit="10"
                                                            :clear-on-search="true"
                                                            :searchable="true"
                                                            :delay="0"
                                                            :min-chars="0"
                                                            :object="true"
                                                            :options="async (query) => {
                                                                return await debouncedGetOptionUsers(query)
                                                            }"
                                                            :can-clear="false"
                                                            @open="debouncedGetOptionUsers('')"
                                                            v-if="checkInputTypeUser(conditionSub.field.type)"
                                                        />
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            :mode="conditionSub.field.multiple ? 'tags' : 'single'"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :filter-results="false"
                                                            :resolve-on-load="false"
                                                            :infinite="true"
                                                            :limit="10"
                                                            :clear-on-search="true"
                                                            :searchable="true"
                                                            :delay="0"
                                                            :min-chars="0"
                                                            :object="true"
                                                            :options="async (query) => {
                                                                return await debouncedGetOptionColumnData(query, conditionSub.field)
                                                            }"
                                                            :can-clear="false"
                                                            @open="debouncedGetOptionColumnData('', conditionSub.field)"
                                                            v-if="checkInputTypeObjectSystem(conditionSub.field.type)"
                                                        />
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            mode="tags"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :object="true"
                                                            :options="state.selectOptionDepartments"
                                                            :can-clear="false"
                                                            v-if="checkInputTypeDepartmentSystem(conditionSub.field.type)"
                                                        >
                                                            <template v-slot:option="{ option }">
                                                                <div class="custom-option">
                                                                    <div class="option-label mb-1">
                                                                        {{ option.label }}
                                                                    </div>
                                                                    <div class="option-description text-secondary">
                                                                        <small>
                                                                            <i>{{ option.type }}</i>
                                                                        </small>
                                                                    </div>
                                                                </div>
                                                            </template>
                                                        </Multiselect>
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            mode="tags"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :object="true"
                                                            :options="state.selectOptionJobPositionSystems"
                                                            :can-clear="false"
                                                            v-if="checkInputTypeJobPositionSystem(conditionSub.field.type)"
                                                        />
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            mode="tags"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :searchable="true"
                                                            :object="true"
                                                            :options="state.selectOptionRanks"
                                                            :can-clear="false"
                                                            v-if="checkInputTypeRank(conditionSub.field.type)"
                                                        />
                                                        <Multiselect
                                                            v-model="conditionSub.value"
                                                            mode="tags"
                                                            :placeholder="$t('workflow.choose')"
                                                            :close-on-select="false"
                                                            :filter-results="false"
                                                            :resolve-on-load="false"
                                                            :infinite="true"
                                                            :limit="10"
                                                            :clear-on-search="true"
                                                            :searchable="true"
                                                            :delay="0"
                                                            :min-chars="0"
                                                            :object="true"
                                                            :options="async (query) => {
                                                                return await debouncedGetOptionUsers(query)
                                                            }"
                                                            :can-clear="false"
                                                            @open="debouncedGetOptionUsers('')"
                                                            v-if="checkInputTypeCreateBy(conditionSub.field.type, conditionSub.field.object)"
                                                        />
                                                        <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            height="24px"
                                                            viewBox="0 -960 960 960"
                                                            width="24px"
                                                            fill="#83868C"
                                                            class="cursor-pointer ms-2 float-end"
                                                            @click="removeConditionSubEdit(index, subIndex)" 
                                                        >
                                                            <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                                <b-dropdown variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-tab">
                                                    <template #button-content>
                                                        <div class="d-flex align-items-center p-1">
                                                            <div 
                                                                class="d-flex align-items-center bg-add-condition cursor-pointer" 
                                                                :title="$t('workflow.condition.add')" 
                                                                v-b-tooltip.hover 
                                                                @click="addConditionSubEdit(index)"
                                                            >
                                                                <span class="material-symbols-outlined">add_circle</span>
                                                                <span class="ms-1 text-secondary">{{ $t('workflow.condition.add') }}</span>
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <div class="px-3 py-2 search-container" @click.stop>
                                                        <BInputGroup>
                                                            <template #append>
                                                                <BInputGroupText>
                                                                    <span class="material-icons-outlined">search</span>
                                                                </BInputGroupText>
                                                            </template>
                                                            <BFormInput :placeholder="$t('search.title')" v-model="state.searchQueryField"/>
                                                        </BInputGroup>
                                                    </div>
                                                    <b-dropdown-divider></b-dropdown-divider>
                                                    <b-dropdown-item 
                                                        v-for="(field, indexField) in filteredOptionFields" 
                                                        :key="indexField" 
                                                        @click="selectFieldEdit(index, field)"
                                                    >
                                                        {{ field.display_name }}
                                                    </b-dropdown-item>
                                                    <div v-if="filteredOptionFields.length === 0" class="px-3 py-2">
                                                        {{ $t('search.no_matching_records_found') }}
                                                    </div>
                                                </b-dropdown>
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                height="24px"
                                                viewBox="0 -960 960 960"
                                                width="24px"
                                                fill="#83868C"
                                                class="cursor-pointer ms-2"
                                                @click="removeConditionEdit(index)" 
                                            >
                                                <path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button 
                                type="button" 
                                class="btn btn-primary mb-3 d-flex align-items-center" 
                                @click="addConditionEdit()"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
                                    <path d="M440-280h80v-160h160v-80H520v-160h-80v160H280v80h160v160Zm40 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/>
                                </svg>
                            </button>
                            <CCardFooter>
                                <div class="d-flex justify-content-end">
                                    <CButton 
                                        type="button"
                                        class="btn btn-light border m-1"
                                        @click="closeEditCondition"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.condition.close') }}
                                        </span>
                                    </CButton>
                                    <CButton 
                                        type="submit"
                                        class="btn btn-primary m-1"
                                    >
                                        <span class="text-uppercase">
                                            {{ $t('workflow.condition.save_update') }}
                                        </span>
                                    </CButton>
                                </div>
                            </CCardFooter>
                        </Form>
                    </BAccordionItem>
                </BAccordion>
            </BTab>
        </BTabs>
    </CCol>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { useToast } from 'vue-toast-notification';
import { useI18n } from "vue-i18n";
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import Multiselect from '@vueform/multiselect';
import useOptions from '@/composables/option';
import useFields from '@/composables/field';
import debounce from 'lodash.debounce';
import cloneDeep from 'lodash/cloneDeep'
import  { generateStandardSlug } from "@/utils/utils";
import { WORKFLOWS } from "@/constants/constants";

export default defineComponent({
    name: 'ConditionAdd',
    emits: ['close-modal-condition', 'reset-modal-condition', 'add-condition', 'edit-condition', 'remove-condition'],

    components: {
        Multiselect,
        Form,
		Field,
		ErrorMessage,
    },

    props: {
        dataCondition: {
            type: Object,
            default: {},
            required: true,
        },
        listDataConditions: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
        fieldSetups: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const { t }  = useI18n();
        const $toast = useToast();

        const schema = () => {
            let schemaForm = yup.object().shape({});

            schemaForm = schemaForm.shape({
                name: yup.string()
                    .required(`${t('workflow.condition.name')} ${t('workflow.condition.validate.required')}`),
                slug: yup.string()
                    .required(`${t('workflow.condition.slug')} ${t('workflow.condition.validate.required')}`),
                conditions: yup.array().of(
                    yup.object().shape({
                        object: yup.string()
                            .required(`${t('workflow.condition.object')} ${t('workflow.condition.validate.required')}`),
                    })
                ),
            });

            return schemaForm;
        }

        const state = reactive({
            tabIndex: 0,
            activeTabEdit: false,
            indexEdit: null as any,
            conditionDetail: {} as any,
            selectOptionObjects: [
				{ label: t('workflow.choose'), value: '' },
				{ label: t('workflow.object.form'), value: WORKFLOWS.CONDITION.OPTION_OBJECT.FORM },
				{ label: t('workflow.object.process'), value: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
			] as Array<any>,
            selectOptionOperators: [
                { label: t('workflow.operator.equal'), value: 'equal' },
                { label: t('workflow.operator.not_equal'), value: 'not_equal' },
                { label: t('workflow.operator.in'), value: 'in' },
                { label: t('workflow.operator.not_in'), value: 'not_in' },
                { label: t('workflow.operator.greater'), value: 'greater' },
                { label: t('workflow.operator.lower'), value: 'lower' },
                { label: t('workflow.operator.not_greater'), value: 'not_greater' },
                { label: t('workflow.operator.not_lower'), value: 'not_lower' },
                { label: t('workflow.operator.contain'), value: 'contain' },
                { label: t('workflow.operator.not_contain'), value: 'not_contain' },
                { label: t('workflow.operator.empty'), value: 'empty' },
                { label: t('workflow.operator.not_empty'), value: 'not_empty' },
                { label: t('workflow.operator.between'), value: 'between' },
                { label: t('workflow.operator.other'), value: 'other' },
			] as Array<any>,
            selectOptionSystems: [
                { display_name: t('workflow.option_condition_system.department'), keyword: "department_ids", type: 'DEPARTMENT_SYSTEM' }, 
                { display_name: t('workflow.option_condition_system.job_position'), keyword: "job_position_ids", type: 'JOB_POSITION_SYSTEM' },
                { display_name: t('workflow.option_condition_system.rank'), keyword: "rank_ids", type: 'RANK_SYSTEM' },
			] as Array<any>,
            selectOptionProcesss: [
                { display_name: t('workflow.option_processs.name'), keyword: "name", type: "VARCHAR", object: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
                { display_name: t('workflow.option_processs.description'), keyword: "description", type: "VARCHAR", object: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
                { display_name: t('workflow.option_processs.create_by'), keyword: "create_by", type: "CREATE_BY", object: WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS },
			] as Array<any>,
            selectOptionLogicalOperators: [
                { label: t('workflow.condition.operator_and'), value: WORKFLOWS.CONDITION.OPERATOR.AND },
                { label: t('workflow.condition.operator_or'), value: WORKFLOWS.CONDITION.OPERATOR.OR },
			] as Array<any>,
            selectOptionFields: [] as Array<any>,
            searchQueryField: '',
            selectOptionDepartments: [] as Array<any>,
            selectOptionJobPositionSystems: [] as Array<any>,
            selectOptionRanks: [] as Array<any>,
        });

        onMounted( async () => {
		});

        const filteredOptionFields = computed(() => {
            return state.selectOptionFields.filter(field => 
                field.display_name.toLowerCase().includes(state.searchQueryField.toLowerCase())
            );
        })

        const closeFormCondition = () => {
            emit('close-modal-condition');
		}

        const mapConditions = (arrayConditions: Array<any>, arrayConditionSubs: Array<any>) => {
            return arrayConditions.map((condition, index) => {
                return {
                    object: condition.object || '', // Lấy giá trị `object` từ `arrayConditions`
                    logicalOperator: condition.logicalOperator || '', // Lấy giá trị `logicalOperator` từ `arrayConditions`
                    filter: {
                        ors: arrayConditionSubs[index]?.map((item: any) => ({
                            f: item.field?.keyword || '', // Key của field
                            o: item.operator?.value || '', // Toán tử
                            p: item.value || '', // Giá trị
                        })) || [] // Nếu không có dữ liệu, trả về mảng rỗng
                    }
                };
            });
        };

        const handleSubmitFormCondition = async () => {
            if (!checkDuplicateSlugAdd(props.dataCondition.slug)) {
                // Kiểm tra các mảng item trong item props.dataCondition.arrayConditionSubs nếu trống sẽ cảnh báo
                if (props.dataCondition.arrayConditionSubs.some((sub: any) => sub.length === 0) || props.dataCondition.arrayConditions.length == 0) {
                    $toast.open({
                        message: t('toast.message.not_setting_condition'),
                        type: "warning",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });

                    return false;
                }
                const mappedData = mapConditions(props.dataCondition.arrayConditions, props.dataCondition.arrayConditionSubs);
                console.log(mappedData);
                
                props.dataCondition.orConditions = mappedData;
                emit("add-condition", props.dataCondition);
                emit('reset-modal-condition');
                state.tabIndex = 1;
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const editConditionList = (index: number, condition: object): void => {
            state.indexEdit = index;
            state.conditionDetail = cloneDeep(condition);
            state.activeTabEdit = true;
		}

        const removeConditionList = (index: number): void => {
            const slugValue = props.listDataConditions[index].value.slug;
			props.listDataConditions.splice(index, 1);
            emit("remove-condition", props.listDataConditions, slugValue);
            closeEditCondition();
		}

        const closeEditCondition = () => {
            state.activeTabEdit = false;
        }

        const handleSubmitEditCondition = async () => {
            if (!checkDuplicateSlugEdit(state.conditionDetail.slug, state.indexEdit)) {
                if (state.conditionDetail.arrayConditionSubs.some((sub: any) => sub.length === 0) || state.conditionDetail.arrayConditions.length == 0) {
                    $toast.open({
                        message: t('toast.message.not_setting_condition'),
                        type: "warning",
                        duration: 5000,
                        dismissible: true,
                        position: "bottom-right",
                    });

                    return false;
                }
                const slugValue = props.listDataConditions[state.indexEdit].value.slug;
                const mappedData = mapConditions(state.conditionDetail.arrayConditions, state.conditionDetail.arrayConditionSubs);
                
                state.conditionDetail.orConditions = mappedData;
                emit("edit-condition", state.conditionDetail, state.indexEdit, slugValue);
                closeEditCondition();
                $toast.open({
                    message: t('toast.status.ACTION_SUCCESS'),
                    type: "success",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
            }
        }

        const addCondition = () => {
            resetValueCondition();
            props.dataCondition.arrayConditions.push({
                object: '',
                logicalOperator: WORKFLOWS.CONDITION.OPERATOR.AND, // Giá trị mặc định
            });
            props.dataCondition.arrayConditionSubs.push([]);
        }

        const addConditionEdit = () => {
            resetValueCondition();
            state.conditionDetail.arrayConditions.push({
                object: '',
                logicalOperator: WORKFLOWS.CONDITION.OPERATOR.AND, // Giá trị mặc định
            });
            state.conditionDetail.arrayConditionSubs.push([]);
        }

        const removeCondition = (index: number): void => {
            props.dataCondition.arrayConditions.splice(index, 1);
            props.dataCondition.arrayConditionSubs.splice(index, 1);
        }

        const removeConditionEdit = (index: number): void => {
            state.conditionDetail.arrayConditions.splice(index, 1);
            state.conditionDetail.arrayConditionSubs.splice(index, 1);
        }

        const resetValueCondition = (): void => {
            state.searchQueryField = '',
            state.selectOptionFields = [];
        }

        const addConditionSub = (index: number) => {
            // Khi click thêm điều kiện sẽ cập nhật selectOptionFields
            resetValueCondition();
            const valueObject = props.dataCondition.arrayConditions[index].object;
            if (valueObject == WORKFLOWS.CONDITION.OPTION_OBJECT.FORM ) {
                // Lấy tất cả các key trong children của các item có type là TABLE
                const tableChildKeys = props.fieldSetups
                    .filter((item: any) => item.type === 'TABLE' && item.childrens)
                    .flatMap((item: any) => item.childrens.map((child: any) => child.value));
                // Lọc các item không có type là table hoặc file và không có key nằm trong tableKeys
                state.selectOptionFields = props.fieldSetups.filter((item: any) => {
                    if (item.type === 'TABLE' || item.type === 'FILEUPLOAD') {
                        return false; // Loại bỏ item có type là TABLE hoặc FILEUPLOAD
                    }

                    if (tableChildKeys.includes(item.keyword)) {
                        return false; // Loại bỏ item có keyword nằm trong tableKeys
                    }

                    return true; // Giữ lại các item khác
                }); 
            } else {
                state.selectOptionFields = [...state.selectOptionSystems, ...state.selectOptionProcesss];
            }
        }

        const addConditionSubEdit = (index: number) => {
            // Khi click thêm điều kiện sẽ cập nhật selectOptionFields
            resetValueCondition();
            const valueObject = state.conditionDetail.arrayConditions[index].object;
            if (valueObject == WORKFLOWS.CONDITION.OPTION_OBJECT.FORM ) {
                // Lấy tất cả các key trong children của các item có type là TABLE
                const tableChildKeys = props.fieldSetups
                    .filter((item: any) => item.type === 'TABLE' && item.childrens)
                    .flatMap((item: any) => item.childrens.map((child: any) => child.value));
                // Lọc các item không có type là table hoặc file và không có key nằm trong tableKeys
                state.selectOptionFields = props.fieldSetups.filter((item: any) => {
                    if (item.type === 'TABLE' || item.type === 'FILEUPLOAD') {
                        return false; // Loại bỏ item có type là TABLE hoặc FILEUPLOAD
                    }

                    if (tableChildKeys.includes(item.keyword)) {
                        return false; // Loại bỏ item có keyword nằm trong tableKeys
                    }

                    return true; // Giữ lại các item khác
                }); 
            } else {
                state.selectOptionFields = [...state.selectOptionSystems, ...state.selectOptionProcesss];
            }
        }

        const removeConditionSub = (index: number, subIndex: number): void => {
            if (props.dataCondition.arrayConditionSubs[index]) {
                props.dataCondition.arrayConditionSubs[index].splice(subIndex, 1);
            }
        }

        const removeConditionSubEdit = (index: number, subIndex: number): void => {
            if (state.conditionDetail.arrayConditionSubs[index]) {
                state.conditionDetail.arrayConditionSubs[index].splice(subIndex, 1);
            }
        }

        const selectField = async (index: number, field: any) => {
            if (!props.dataCondition.arrayConditionSubs[index]) {
                props.dataCondition.arrayConditionSubs[index] = [];
            }
            const filteredOperators = filterOperatorsByType(field.type);
            props.dataCondition.arrayConditionSubs[index].push({ field, operator: filteredOperators[0], filteredOperators });
            if (field.type == 'DEPARTMENT') {
                await getOptionDepartments();
            }

            if (field.type == 'DEPARTMENT_SYSTEM') {
                await getOptionDepartments();
            }

            if (field.type == 'JOB_POSITION_SYSTEM') {
                await getOptionJobPositionSystems();
            }

            if (field.type == 'RANK_SYSTEM') {
                await getOptionRanks();
            }

            // console.log(props.dataCondition.arrayConditionSubs);
        }

        const selectFieldEdit = async (index: number, field: any) => {
            if (!state.conditionDetail.arrayConditionSubs[index]) {
                state.conditionDetail.arrayConditionSubs[index] = [];
            }
            const filteredOperators = filterOperatorsByType(field.type);
            state.conditionDetail.arrayConditionSubs[index].push({ field, operator: filteredOperators[0], filteredOperators });
            if (field.type == 'DEPARTMENT') {
                await getOptionDepartments();
            }

            if (field.type == 'DEPARTMENT_SYSTEM') {
                await getOptionDepartments();
            }

            if (field.type == 'JOB_POSITION_SYSTEM') {
                await getOptionJobPositionSystems();
            }

            if (field.type == 'RANK_SYSTEM') {
                await getOptionRanks();
            }
            // console.log(state.conditionDetail.arrayConditionSubs);
        }

        const filterOperatorsByType = (value: string) => {
            const operatorValues = {
                VARCHAR: ['contain', 'not_contain'],
                TEXT: ['contain', 'not_contain'],
                INTEGER: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'],
                FLOAT: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'],
                DATE: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'], 
                RADIO: ['in', 'not_in', 'empty', 'not_empty'],
                TIME: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'empty', 'not_empty', 'other'],
                SELECT: ['in', 'not_in', 'empty', 'not_empty'],
                CHECKLIST: ['in', 'not_in', 'empty', 'not_empty'],
                USER: ['in', 'not_in', 'empty', 'not_empty'],
                DEPARTMENT: ['in', 'not_in', 'empty', 'not_empty'],
                FORMULA: ['equal', 'greater', 'not_lower', 'lower', 'not_greater', 'between', 'empty', 'not_empty', 'other'],
                OBJECTSYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                DEPARTMENT_SYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                JOB_POSITION_SYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                RANK_SYSTEM: ['in', 'not_in', 'empty', 'not_empty'],
                CREATE_BY: ['in', 'not_in', 'empty', 'not_empty'],
            };

            const allowedValues = operatorValues[value] || [];

            return state.selectOptionOperators.filter(op => allowedValues.includes(op.value));
        }

        const selectOperator = (index: number, subIndex: number, operator: any) => {
            if (props.dataCondition.arrayConditionSubs[index] && props.dataCondition.arrayConditionSubs[index][subIndex]) {
                const conditionSub = props.dataCondition.arrayConditionSubs[index][subIndex];
                conditionSub.operator = operator;
                // console.log(operator);
                // Nếu toán tử là 'empty' hoặc 'not_empty', xóa giá trị và vô hiệu hóa nhập liệu
                if (operator.value === 'empty' || operator.value === 'not_empty') {
                    conditionSub.value = null; // Hoặc '', tùy thuộc vào yêu cầu
                    conditionSub.disableInput = true; // Thêm thuộc tính để vô hiệu hóa trường nhập
                } else {
                    conditionSub.disableInput = false; // Cho phép nhập lại nếu toán tử khác
                }
                if (operator.value === 'between') {
                    // Khởi tạo giá trị là mảng [null, null] để lưu hai giá trị
                    conditionSub.value = [null, null];
                } else {
                    // Nếu không phải 'between', đặt giá trị về null
                    conditionSub.value = null;
                }
            }
        }

        const selectLogicalOperator = (index: number, logicalOperator: string) => {
            if (props.dataCondition.arrayConditions[index]) {
                props.dataCondition.arrayConditions[index].logicalOperator = logicalOperator;
            }
        }

        const selectOperatorEdit = (index: number, subIndex: number, operator: any) => {
            if (state.conditionDetail.arrayConditionSubs[index] && state.conditionDetail.arrayConditionSubs[index][subIndex]) {
                const conditionSub = state.conditionDetail.arrayConditionSubs[index][subIndex];
                conditionSub.operator = operator;
                // console.log(operator);
                // Nếu toán tử là 'empty' hoặc 'not_empty', xóa giá trị và vô hiệu hóa nhập liệu
                if (operator.value === 'empty' || operator.value === 'not_empty') {
                    conditionSub.value = null; // Hoặc '', tùy thuộc vào yêu cầu
                    conditionSub.disableInput = true; // Thêm thuộc tính để vô hiệu hóa trường nhập
                } else {
                    conditionSub.disableInput = false; // Cho phép nhập lại nếu toán tử khác
                }
                if (operator.value === 'between') {
                    // Khởi tạo giá trị là mảng [null, null] để lưu hai giá trị
                    conditionSub.value = [null, null];
                } else {
                    // Nếu không phải 'between', đặt giá trị về null
                    conditionSub.value = null;
                }
            }
        }

        const getOperatorLabel = (conditionSub: any) => {
            return conditionSub.operator ? conditionSub.operator.label : (conditionSub.filteredOperators[0] ? conditionSub.filteredOperators[0].label : '');
        }

        const getLogicalOperatorLabel = (logicalOperator: string) => {
            return logicalOperator == WORKFLOWS.CONDITION.OPERATOR.AND ? t('workflow.condition.operator_and') : t('workflow.condition.operator_or');
        }
        
        const checkInputTypeText = (type: string) => {
            if (type == 'VARCHAR' || type == 'TEXT' || type == 'FORMULA') {
                return true;
            }
        }

        const checkInputTypeNumber = (type: string) => {
            if (type == 'INTEGER' || type == 'FLOAT') {
                return true;
            }
        }

        const checkInputTypeTime = (type: string) => {
            if (type == 'TIME') {
                return true;
            }
        }

        const checkInputTypeDate = (type: string) => {
            if (type == 'DATE') {
                return true;
            }
        }

        const checkInputTypeSelect = (type: string) => {
            if (type == 'RADIO' || type == 'CHECKLIST' || type == 'SELECT') {
                return true;
            }
        }

        const checkInputTypeUser = (type: string) => {
            if (type == 'USER') {
                return true;
            }
        }

        const checkInputTypeDepartment = (type: string) => {
            if (type == 'DEPARTMENT') {
                return true;
            }
        }

        const checkInputTypeObjectSystem = (type: string) => {
            if (type == 'OBJECTSYSTEM') {
                return true;
            }
        }

        const checkInputTypeDepartmentSystem = (type: string) => {
            if (type == 'DEPARTMENT_SYSTEM') {
                return true;
            }
        }

        const checkInputTypeJobPositionSystem = (type: string) => {
            if (type == 'JOB_POSITION_SYSTEM') {
                return true;
            }
        }

        const checkInputTypeRank = (type: string) => {
            if (type == 'RANK_SYSTEM') {
                return true;
            }
        }

        const checkInputTypeCreateBy = (type: string, object: string) => {
            if (type == 'CREATE_BY' && object == WORKFLOWS.CONDITION.OPTION_OBJECT.PROCESS) {
                return true;
            }
        }

        const { getUsers, getDepartments, getJobPositionSystems, getRankSystems } = useOptions();
        const { getColumnDatas } = useFields();

        const getOptionDepartments = async () => {
            let result = await getDepartments();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionDepartments = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
                        type: elem.type,
					} 
				));
            }
		}

        const getOptionJobPositionSystems = async () => {
            let result = await getJobPositionSystems();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionJobPositionSystems = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
					} 
				));
            }
		}

        const getOptionRanks = async () => {
            let result = await getRankSystems();
            if (Array.isArray(result) && result.length > 0) {
                state.selectOptionRanks = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
					} 
				));
            }
		}

        const getOptionUsers = async (query: string) => {
			let result = await getUsers(query);
			if (Array.isArray(result) && result.length > 0) {
                return result.map((elem: any) => (
                    {
                        value: elem.id,
                        label: `${elem.account_name} - ${elem.full_name}`,
                    } 
                ));
            }
		}

		const debouncedGetOptionUsers = debounce(getOptionUsers, 500);

        const getOptionColumnData = async (query: string, field: any) => {
			let result = await getColumnDatas(field.object_table, field.sub_column_table, field.column_table, query);
			if (!!result && Array.isArray(result.data_options) && result.data_options.length > 0) {
                return result.data_options.map((elem: any) => (
					{
						value: elem.id,
						label: elem[field.column_table],
					} 
				));
            }
		}

		const debouncedGetOptionColumnData = debounce(getOptionColumnData, 500);

        const handleChangeObject = (index: number) => {
            props.dataCondition.arrayConditionSubs[index] = [];
        }

        const handleInputNameAdd = async (valueName: any) => {
            if (!valueName) {
                props.dataCondition.slug = '';
			} else {
    			props.dataCondition.slug = generateStandardSlug(valueName);
			}
		}

        const handleInputNameEdit = async (valueName: any) => {
            if (!valueName) {
                state.conditionDetail.slug = '';
			} else {
    			state.conditionDetail.slug = generateStandardSlug(valueName);
			}
		}

        const checkDuplicateSlugAdd = (slug: string): boolean => {
            return props.listDataConditions.some((item: any) => item.value.slug === slug);
        }

        const checkDuplicateSlugEdit = (slug: string, indexEdit: number): boolean => {
            return props.listDataConditions.some((item: any, index: number) => {
                // Bỏ qua kiểm tra nếu là chính slug hiện tại (indexEdit)
                if (index === indexEdit) {
                    return false;
                }
                return item.value.slug === slug;
            });
        }

        return {
            state,
            schema,
            closeFormCondition,
            filteredOptionFields,
            handleSubmitFormCondition,
            editConditionList,
            removeConditionList,
            closeEditCondition,
            handleSubmitEditCondition,
            addCondition,
            removeCondition,
            addConditionSub,
            removeConditionSub,
            selectField,
            selectOperator,
            selectLogicalOperator,
            addConditionEdit,
            removeConditionEdit,
            addConditionSubEdit,
            removeConditionSubEdit,
            selectFieldEdit,
            selectOperatorEdit,
            getOperatorLabel,
            getLogicalOperatorLabel,
            checkInputTypeText,
            checkInputTypeNumber,
            checkInputTypeTime,
            checkInputTypeDate,
            checkInputTypeSelect,
            checkInputTypeUser,
            checkInputTypeDepartment,
            checkInputTypeObjectSystem,
            checkInputTypeDepartmentSystem,
            checkInputTypeJobPositionSystem,
            checkInputTypeRank,
            checkInputTypeCreateBy,
            debouncedGetOptionUsers,
            debouncedGetOptionColumnData,
            handleChangeObject,
            handleInputNameAdd,
            handleInputNameEdit,
            checkDuplicateSlugAdd,
            checkDuplicateSlugEdit
        }
    },
});
</script>
<style type="text/css" scoped>
.card-footer {
    z-index: 99;
    position: sticky;
    left: 0px;
    bottom: 0px;
    width: 100%;
    background-color:#f8f9fa;
    padding: 10px;
}
svg {
    cursor: pointer;
}
.table__td--action {
    min-width: 70px !important;
}
.condition-container {
    background-color: #f7f8f9;
    border-radius: 0.175rem;
    padding: 15px;
    position: relative;
}
.bg-add-condition {
    background-color: #eee;
    border-radius: 0.175rem;
    padding: 5px;
}
.cursor-pointer {
    cursor: pointer;
}
.filter-container {
    padding: 15px;
}
.form-select {
    line-height: 1.875;
}
.dropdown-operator {
    margin-top: 0px !important;
}
.bg-select-logical-operator {
    background-color: #eee;
    border-radius: 0.175rem;
    padding-top: 3px;
    padding-right: 10px;
    padding-bottom: 3px;
    padding-left: 10px;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>