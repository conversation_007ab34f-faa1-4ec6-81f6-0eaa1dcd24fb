<?php

namespace App\Services;

use App\Repositories\SaveJob\SaveJobRepositoryInterface;
use App\Repositories\JobApprovalHistory\JobApprovalHistoryRepositoryInterface;
use App\Models\SaveJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Enums\SaveJobStatus;
use App\Enums\FieldStatus;
use App\Repositories\User\UserRepositoryInterface;

class JobPermissionService
{
    protected $saveJobRepository;
    protected $jobApprovalHistoryRepository;
    protected $userRepository;

    public function __construct(
        SaveJobRepositoryInterface $saveJobRepository,
        JobApprovalHistoryRepositoryInterface $jobApprovalHistoryRepository,
        UserRepositoryInterface $userRepository
    ) {
        $this->saveJobRepository = $saveJobRepository;
        $this->jobApprovalHistoryRepository = $jobApprovalHistoryRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Lấy tất cả các công việc mà người dùng có thể xem
     * Bao gồm các thông tin về quyền hạn và lịch sử phê duyệt 
     * để hiển thị trên một trang job duy nhất
     */
    public function getJobsWithPermissions($userId, $dataSearch)
    {
        $tab = $dataSearch['tab'] ?? SaveJobStatus::ALL->value;
        $page = $dataSearch['page'] ?? null;
        $perPage = $dataSearch['perPage'] ?? null;

        $user = User::find($userId);
        if (!$user) {
            return collect();
        }

        $query = SaveJob::query()
            ->with([
                'jobApprovalHistories' => function($query) use ($userId) {
                    $query->orderBy('created_at', 'asc');
                },
                'jobApprovalHistories.actor',
                'jobApprovalHistories.stage',
                'assignedUser',
                'createdBy',
                'department',
                'jobPosition',
                'processVersion:id,process_id', 
                'processVersion.process:id,name,process_group_id', 
                'processVersion.process.processGroup:id,name',
            ])
            ->where(function($query) use ($userId) {
                $query->whereJsonContains('managers', $userId)
                    ->orWhereJsonContains('followers', $userId)
                    ->orWhere('create_by', $userId)
                    ->orWhere('user_id', $userId)
                    ->orWhereHas('jobApprovalHistories', function($q) use ($userId) {
                        $q->whereJsonContains('approved_list', $userId)
                            ->orWhereJsonContains('followers', $userId);
                    });
            });

        // Tạo baseQuery để sử dụng cho việc đếm các tab
        $baseQuery = clone $query;

        // Áp dụng điều kiện theo tab và các điều kiện tìm kiếm khác cho query chính
        if ($tab !== SaveJobStatus::ALL->value) {
            $query->where('status', $tab);
        }

        if (isset($dataSearch['keyword'])) {
            $keyword = $dataSearch['keyword'];
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('description', 'like', "%{$keyword}%");
            });
            
            // Cũng áp dụng tìm kiếm keyword cho baseQuery
            $baseQuery->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                    ->orWhere('description', 'like', "%{$keyword}%");
            });
        }
        
        $orderBy = $dataSearch['orderBy'] ?? 'created_at';
        $orderDirection = $dataSearch['orderDirection'] ?? 'desc';
        $query->orderBy($orderBy, $orderDirection);
        
        $statusList = [
            SaveJobStatus::ALL->value,
            SaveJobStatus::PENDING->value,
            SaveJobStatus::PROCESSING->value,
            SaveJobStatus::COMPLETED->value,
            SaveJobStatus::CANCEL->value,
        ];

        $counts = [];
        foreach ($statusList as $status) {
            $countQuery = clone $baseQuery;
            if ($status !== SaveJobStatus::ALL->value) {
                $countQuery->where('status', $status);
            }
            $counts[$status] = $countQuery->count();
        }

        if ($page && $perPage) {
            $jobs = $query->paginate($perPage, ['*'], 'page', $page);
        } else {
            $jobs = $query->get();
        }
        
        $this->attachPermissionsToJobs($jobs, $user);
        
        return [
            'jobs' => $jobs,
            'counts' => $counts
        ];
    }
    
    /**
     * Đính kèm thông tin quyền hạn vào từng công việc trong collection
     */
    protected function attachPermissionsToJobs($jobs, User $user)
    {
        foreach ($jobs as $job) {
            // Sử dụng biến tạm thời để lưu trữ quyền hạn
            $permissions = [
                'can_view' => Gate::allows('view', $job),
                'can_edit' => Gate::allows('update', $job),
                'can_delete' => Gate::allows('delete', $job),
                'can_approve' => Gate::allows('approve', $job),
                'can_follow' => Gate::allows('toggleFollow', $job),
                // Thêm luôn vai trò vào đây thay vì sửa đổi sau
                'role' => $this->determineUserRole(
                    $job->isManager($user), 
                    $job->isFollower($user), 
                    $job->isCreator($user), 
                    $job->isAssignee($user)
                )
            ];
            
            // Gán tất cả quyền trong một lần duy nhất
            $job->permissions = $permissions;
            
            // Kiểm tra quyền phê duyệt cho từng lịch sử phê duyệt
            if ($permissions['can_approve']) {
                // Trả về tất cả các stage mà user có thể phê duyệt
                $job->pending_approval = $job->getAllApprovalStagesForUser($user);
            }
            
            // Xác định trạng thái hiển thị của công việc với người dùng
            $job->display_status = $this->determineJobDisplayStatus($job, $permissions);
        }
    }
    
    /**
     * Xác định vai trò chính của người dùng đối với công việc
     */
    protected function determineUserRole($isManager, $isFollower, $isCreator, $isAssignee)
    {
        if ($isManager) return 'manager';
        if ($isCreator) return 'creator';
        if ($isAssignee) return 'assignee';
        if ($isFollower) return 'follower';
        return 'approver'; // Nếu không có vai trò nào khác, người dùng có thể là người phê duyệt
    }
    
    /**
     * Xác định trạng thái hiển thị của công việc
     */
    protected function determineJobDisplayStatus($job, $permissions)
    {
        // Nếu người dùng có quyền phê duyệt và công việc đang chờ xử lý
        if ($permissions['can_approve'] && $job->status === 'pending') {
            return 'pending_approval';
        }
        
        // Nếu là người quản lý hoặc người tạo
        if ($permissions['can_edit']) {
            return 'manageable';
        }
        
        // Các trường hợp khác
        switch ($job->status) {
            case 'completed':
                return 'completed';
            case 'processing':
                return 'in_progress';
            case 'cancel':
                return 'canceled';
            default:
                return 'view_only';
        }
    }
    
    /**
     * Lấy chi tiết công việc cùng thông tin quyền hạn
     */
    public function getJobDetail($jobId, $userId)
    {
        // Lấy thông tin job
        $job = $this->saveJobRepository->find($jobId);
        
        if (!$job) {
            return null;
        }
        
        // Lấy người dùng
        $user = User::find($userId);
        if (!$user) {
            return null;
        }
        
        // Kiểm tra quyền xem
        if (!Gate::forUser($user)->allows('view', $job)) {
            return null; // Không có quyền xem
        }
        
        // Lấy ngôn ngữ hiện tại của người dùng (chỉ lấy một lần)
        $currentLocale = app()->getLocale(); // 'vi' hoặc 'en'
        $isVietnamese = $currentLocale === 'vi';
        
        // Lấy danh sách các model có thể được liên kết
        $modelSystemRepository = app(\App\Repositories\ModelSystem\ModelSystemRepositoryInterface::class);
        $availableModels = $modelSystemRepository->getModelSystem();
        
        // Tải quan hệ cần thiết
        $job->load([
            'jobApprovalHistories:id,job_id,stage_id,user_id,action_id,date,comment,updated_at',
            'jobApprovalHistories' => function($query) {
                $query->with(['actor:id,full_name', 'stage:id,name', 'action:id,name'])->orderBy('updated_at', 'desc');
            },
            'assignedUser:id,rank_id',
            'assignedUser.rank:id,name',
            'createdBy:id,full_name',
            'jobPosition:id,name',
            'department:id,name',
            'processVersion:id,process_id',
            'processVersion.process:id,name,form_id',
            'jobFieldValues' => function($query) {
                $query->with(['valueObjects', 'field']);
            }
        ]);
        
        // Xử lý các đối tượng polymorphic sau khi load
        // Nhóm các jobFieldValues theo parent_id để dễ xử lý
        $parentFields = $job->jobFieldValues->whereNull('field.parent_id');
        $childFields = $job->jobFieldValues->whereNotNull('field.parent_id')->groupBy('field.parent_id');
        
        // Tạo mảng để lưu trữ kết quả đã xử lý cho các trường cha
        $processedParentFields = [];

        // Xử lý các trường cha trước
        foreach ($parentFields as $parentField) {
            // Xử lý và lưu trữ giá trị của trường cha
            $this->processFieldValue($parentField, $availableModels, $isVietnamese);
            $processedParentFields[$parentField->field_id] = $parentField;
        }

        // Xử lý và gộp các trường con vào trường cha
        foreach ($childFields as $parentId => $fieldGroup) {
            // Tìm trường cha tương ứng
            $parentField = $processedParentFields[$parentId] ?? null;
            
            if ($parentField) {
                // Tạo mảng để lưu trữ tất cả giá trị của các trường con
                $rowsData = [];
                $columnsMap = [];
                $columnsTypeMap = []; // Thêm map để lưu trữ loại trường
                $rowCount = 0;
                
                // Tạo ánh xạ giữa field_id và label từ field
                foreach ($fieldGroup as $childField) {
                    if ($childField->field) {
                        // Sử dụng display_name hoặc display_name_en làm key tùy theo ngôn ngữ
                        $columnKey = $isVietnamese ? 
                            ($childField->field->display_name ?? $childField->field->keyword) : 
                            ($childField->field->display_name_en ?? $childField->field->display_name ?? $childField->field->keyword);
                        
                        $columnsMap[$childField->field_id] = $columnKey;
                        
                        // Lưu trữ thông tin loại trường vào columnsTypeMap
                        $columnsTypeMap[$columnKey] = $childField->field->type;
                        
                        // Xác định số lượng hàng dựa trên trường con đầu tiên có giá trị
                        if ($rowCount === 0) {
                            if (!empty($childField->extracted_values)) {
                                $rowCount = count($childField->extracted_values);
                            } elseif (!empty($childField->field_value) && is_array($childField->field_value)) {
                                $rowCount = count($childField->field_value);
                            }
                        }
                    }
                }
                
                // Khởi tạo cấu trúc dữ liệu hàng
                for ($i = 0; $i < $rowCount; $i++) {
                    $rowData = [];
                    foreach ($columnsMap as $columnKey) {
                        $rowData[$columnKey] = "";
                    }
                    $rowsData[] = $rowData;
                }
                
                // Điền dữ liệu vào cấu trúc hàng
                foreach ($fieldGroup as $childField) {
                    // Xử lý trường con
                    $this->processFieldValue($childField, $availableModels, $isVietnamese);
                    
                    $columnKey = $columnsMap[$childField->field_id] ?? null;
                    if ($columnKey) {
                        $values = !empty($childField->extracted_values) ? 
                                $childField->extracted_values : 
                                $childField->field_value;
                        
                        if (is_array($values)) {
                            foreach ($values as $rowIndex => $value) {
                                if (isset($rowsData[$rowIndex])) {
                                    $rowsData[$rowIndex][$columnKey] = $value;
                                }
                            }
                        }
                    }
                }
                
                // Gán cấu trúc dữ liệu vào trường cha
                $parentField->table_values = $rowsData;
                
                // Thêm thông tin metadata về loại trường cho table_values
                $parentField->table_columns_type = $columnsTypeMap;
                
                // Vẫn giữ lại cấu trúc child_values để tương thích ngược
                $childValues = [];
                foreach ($fieldGroup as $childField) {
                    if (isset($childField->extracted_values) && !empty($childField->extracted_values)) {
                        $childValues[$childField->field_id] = $childField->extracted_values;
                    } else if (isset($childField->field_value)) {
                        $childValues[$childField->field_id] = $childField->field_value;
                    }
                }
                
                if (!empty($childValues)) {
                    $parentField->child_values = $childValues;
                }
            }
        }
        
        // Xử lý các jobApprovalHistories để lấy thông tin người dùng từ approved_list và followers
        if ($job->jobApprovalHistories) {
            foreach ($job->jobApprovalHistories as $history) {
                // Xử lý approved_list
                if (isset($history->approved_list) && !empty($history->approved_list)) {
                    $approvedList = $history->approved_list;
                    // Không cần json_decode vì model đã sử dụng $casts
                    
                    if (is_array($approvedList) && !empty($approvedList)) {
                        // Kiểm tra cấu trúc mảng để đảm bảo có thể trích xuất user_id
                        $approverIds = [];
                        foreach ($approvedList as $approver) {
                            if (is_array($approver) && isset($approver['user_id'])) {
                                $approverIds[] = $approver['user_id'];
                            } elseif (is_string($approver)) {
                                $approverIds[] = $approver;
                            }
                        }
                        
                        if (!empty($approverIds)) {
                            $approvers = User::whereIn('id', $approverIds)->get()->keyBy('id');
                            
                            // Cập nhật thông tin người dùng vào mảng approved_list
                            $approvedListWithUsers = [];
                            foreach ($approvedList as $index => $approver) {
                                $userId = is_array($approver) ? ($approver['user_id'] ?? null) : $approver;
                                if ($userId && isset($approvers[$userId])) {
                                    if (is_array($approver)) {
                                        $approver['user_info'] = $approvers[$userId];
                                        $approvedListWithUsers[] = $approver;
                                    } else {
                                        $approvedListWithUsers[] = [
                                            'user_id' => $userId,
                                            'user_info' => $approvers[$userId]
                                        ];
                                    }
                                }
                            }
                            $history->approved_list_users = $approvedListWithUsers;
                        }
                    }
                }
                
                // Xử lý followers
                if (isset($history->followers) && !empty($history->followers)) {
                    $historyFollowers = $history->followers;
                    // Không cần json_decode vì model đã sử dụng $casts
                    
                    if (is_array($historyFollowers) && !empty($historyFollowers)) {
                        // Kiểm tra cấu trúc mảng để đảm bảo có thể trích xuất user_id
                        $historyFollowerIds = [];
                        foreach ($historyFollowers as $follower) {
                            if (is_array($follower) && isset($follower['user_id'])) {
                                $historyFollowerIds[] = $follower['user_id'];
                            } elseif (is_string($follower)) {
                                $historyFollowerIds[] = $follower;
                            }
                        }
                        
                        if (!empty($historyFollowerIds)) {
                            $historyFollowerUsers = User::whereIn('id', $historyFollowerIds)->get()->keyBy('id');
                            
                            // Cập nhật thông tin người dùng vào mảng followers
                            $followersWithUsers = [];
                            foreach ($historyFollowers as $index => $follower) {
                                $userId = is_array($follower) ? ($follower['user_id'] ?? null) : $follower;
                                if ($userId && isset($historyFollowerUsers[$userId])) {
                                    if (is_array($follower)) {
                                        $follower['user_info'] = $historyFollowerUsers[$userId];
                                        $followersWithUsers[] = $follower;
                                    } else {
                                        $followersWithUsers[] = [
                                            'user_id' => $userId,
                                            'user_info' => $historyFollowerUsers[$userId]
                                        ];
                                    }
                                }
                            }
                            $history->follower_users = $followersWithUsers;
                        }
                    }
                }
            }
        }
        
        // Lấy thông tin về phiên bản quy trình
        $processVersionId = $job->process_version_id;
        
        // Lấy tất cả các chuyển tiếp giai đoạn trong quy trình
        $stageTransitions = \App\Models\StageTransition::where('process_version_id', $processVersionId)
            ->with([
                'stageTransitionConditions.condition',
                'fromStage',
                'toStage',
                'backToStage',
                'action'
            ])
            ->get();

        // Lấy trạng thái của tất cả các giai đoạn cho job này
        $stageStatuses = \App\Models\ProcessInstanceStageStatus::where('job_id', $job->id)
            ->get()
            ->keyBy('stage_id');
            
        // Lấy thông tin về các cấu hình email cho giai đoạn
        $stageEmailConfigs = \App\Models\StageEmailConfig::where('process_version_id', $processVersionId)
            ->with(['template', 'emailConditions.condition', 'action'])
            ->get()
            ->groupBy(['stage_id', 'action_id']);
        
        // Lấy thông tin về các nhóm đồng bộ
        $stageSyncGroups = \App\Models\StageSyncGroup::whereHas('syncGroup', function($query) use ($processVersionId) {
                $query->where('process_version_id', $processVersionId);
            })
            ->with(['syncGroup', 'action'])
            ->get()
            ->groupBy(['stage_id', 'action_id']);
            
        // Lấy thông tin về các chuyển tiếp quy trình (process transitions)
        $processTransitions = \App\Models\ProcessTransition::where('process_version_id', $processVersionId)
            ->with(['processTransitionsConditions.condition', 'action'])
            ->get()
            ->groupBy(['from_stage_id', 'action_id']);
            
        // Lấy tất cả các stage mà user có thể phê duyệt
        $allApprovalStages = $job->getAllApprovalStagesForUser($user);
        $allStageIds = $allApprovalStages->pluck('stage_id')->toArray();
            
        // Xây dựng cấu trúc flow_transitions đầy đủ
        $flowTransitions = [];
        
        foreach ($stageTransitions as $transition) {
            $fromStageId = $transition->from_stage_id;
            $actionId = $transition->action_id;
            $toStageId = $transition->to_stage_id;
            $backToStageId = $transition->back_to_stage_id;
            
            // Lấy thông tin điều kiện chuyển tiếp
            $conditions = [];
            foreach ($transition->stageTransitionConditions as $condition) {
                $conditions[] = [
                    'id' => $condition->condition_id,
                    'name' => $condition->condition->name ?? null,
                    'status' => $condition->condition_status,
                    'or_conditions' => $condition->condition->or_conditions ?? null
                ];
            }
            
            // Lấy thông tin email template cho action này
            $emailTemplates = isset($stageEmailConfigs[$fromStageId][$actionId]) 
                ? $stageEmailConfigs[$fromStageId][$actionId]->map(function($config) {
                    return [
                        'id' => $config->template_id,
                        'name' => $config->template->name ?? null,
                        'subject' => $config->template->name_title ?? null,
                        'from_email' => $config->template->from_email ?? null,
                        'to_emails' => $config->template->to_emails ?? null,
                        'cc_emails' => $config->template->cc_emails ?? null,
                        'bcc_emails' => $config->template->bcc_emails ?? null,
                        'content' => $config->template->content ?? null,
                        'conditions' => $config->emailConditions->map(function($ec) {
                            return [
                                'id' => $ec->condition_id,
                                'name' => $ec->condition->name ?? null,
                                'status' => $ec->condition_status
                            ];
                        })->toArray()
                    ];
                })->toArray() 
                : [];
                
            // Lấy thông tin sync group cho action này
            $syncGroups = isset($stageSyncGroups[$fromStageId][$actionId]) 
                ? $stageSyncGroups[$fromStageId][$actionId]->map(function($sg) {
                    return [
                        'id' => $sg->sync_group_id,
                        'name' => $sg->syncGroup->name ?? null
                    ];
                })->toArray() 
                : [];
                
            // Lấy thông tin process transition cho action này
            $processTransitionData = isset($processTransitions[$fromStageId][$actionId]) 
                ? $processTransitions[$fromStageId][$actionId]->map(function($pt) {
                    return [
                        'id' => $pt->id,
                        'name' => $pt->name,
                        'to_process_id' => $pt->to_process_id,
                        'conditions' => $pt->processTransitionsConditions->map(function($ptc) {
                            return [
                                'id' => $ptc->condition_id,
                                'name' => $ptc->condition->name ?? null,
                                'status' => $ptc->condition_status
                            ];
                        })->toArray()
                    ];
                })->toArray() 
                : [];
                
            // Xử lý thông tin quay lại (back information)
            $actionBackTo = null;
            $emailTemplateBack = null;

            // Chỉ xử lý khi có back_to_stage_id
            if ($backToStageId) {
                // 'back_to' là hành động mặc định của hệ thống
                $actionBackTo = [
                    'id' => 'back_to',
                    'name' => 'Quay lại',
                ];

                // Tìm email_template_back từ stageEmailConfigs dựa trên from_stage_id và action_id = 'back_to'
                $emailTemplateBack = isset($stageEmailConfigs[$fromStageId]['back_to']) 
                ? $stageEmailConfigs[$fromStageId]['back_to']->map(function($config) {
                    return [
                        'id' => $config->template_id,
                        'name' => $config->template->name ?? null,
                        'subject' => $config->template->name_title ?? null,
                        'from_email' => $config->template->from_email ?? null,
                        'to_emails' => $config->template->to_emails ?? null,
                        'cc_emails' => $config->template->cc_emails ?? null,
                        'bcc_emails' => $config->template->bcc_emails ?? null,
                        'content' => $config->template->content ?? null,
                        'conditions' => $config->emailConditions->map(function($ec) {
                            return [
                                'id' => $ec->condition_id,
                                'name' => $ec->condition->name ?? null,
                                'status' => $ec->condition_status
                            ];
                        })->toArray()
                    ];
                })->toArray() 
                : [];
            }

            // Kiểm tra xem người dùng có quyền thực thi hành động này không
            $canExecute = false;
            if (Gate::forUser($user)->allows('approve', $job)) {
                // Kiểm tra nếu user có thể phê duyệt ở stage hiện tại hoặc bất kỳ stage nào trong danh sách
                if (in_array($fromStageId, $allStageIds)) {
                    $canExecute = true;
                }
            }
            // Xây dựng cấu trúc dữ liệu cho mỗi chuyển tiếp
            $flowTransitions[] = [
                'id' => $transition->id,
                'from_stage' => [
                    'id' => $fromStageId,
                    'name' => $this->getSpecialStageName($fromStageId, $transition->fromStage->name ?? null),
                    'description' => $transition->fromStage->description ?? null,
                    'approvers' => array_column($this->userRepository->getUserByOptionScopeRes($transition->fromStage->approver ?? []), 'label') ?? null,
                    'followers' => array_column($this->userRepository->getUserByOptionScopeRes($transition->fromStage->followers ?? []), 'label') ?? null,
                    'comment' => $transition->fromStage ? ($transition->fromStage->comment == FieldStatus::TRUE->value ? true : false) : false,
                    'status' => $stageStatuses->has($fromStageId) ? $stageStatuses->get($fromStageId)->status : null,
                    'status_updated_at' => $stageStatuses->has($fromStageId) ? $stageStatuses->get($fromStageId)->updated_at : null
                ],
                'action' => [
                    'id' => $actionId,
                    'name' => $this->getSpecialActionName($actionId, $transition->action->name ?? null),
                    'description' => $transition->action->description ?? null
                ],
                'to_stage' => [
                    'id' => $toStageId,
                    'name' => $this->getSpecialStageName($toStageId, $transition->toStage->name ?? null),
                    'description' => $transition->toStage->description ?? null,
                    'approvers' => array_column($this->userRepository->getUserByOptionScopeRes($transition->toStage->approver ?? []), 'label') ?? null,
                    'followers' => array_column($this->userRepository->getUserByOptionScopeRes($transition->toStage->followers ?? []), 'label') ?? null,
                    'comment' => $transition->toStage ? ($transition->toStage->comment == FieldStatus::TRUE->value ? true : false) : false,
                    'status' => $stageStatuses->has($toStageId) ? $stageStatuses->get($toStageId)->status : null,
                    'status_updated_at' => $stageStatuses->has($toStageId) ? $stageStatuses->get($toStageId)->updated_at : null
                ],
                'conditions' => $conditions,
                'email_templates' => $emailTemplates,
                'sync_groups' => $syncGroups,
                'back_to_stage' => $backToStageId ? [
                    'id' => $backToStageId,
                    'name' => $this->getSpecialStageName($backToStageId, $transition->backToStage->name ?? null),
                    'description' => $transition->backToStage->description ?? null,
                    'status' => $stageStatuses->has($backToStageId) ? $stageStatuses->get($backToStageId)->status : null,
                    'status_updated_at' => $stageStatuses->has($backToStageId) ? $stageStatuses->get($backToStageId)->updated_at : null
                ] : null,
                'action_back_to' => $actionBackTo,
                'email_template_back' => $emailTemplateBack,
                'process_transition' => $processTransitionData,
                'can_execute' => $canExecute
            ];
        }
            
        // Đính kèm flow_transitions vào job
        $job->flow_transitions = $flowTransitions;

        // Đính kèm thông tin về tất cả các stage mà user có thể phê duyệt
        $job->all_approval_stages = $allApprovalStages;

        // Đính kèm thông tin về tất cả trạng thái giai đoạn của job này
        $job->stage_statuses = $stageStatuses->map(function($status) {
            return [
                'stage_id' => $status->stage_id,
                'status' => $status->status,
                'created_at' => $status->created_at,
                'updated_at' => $status->updated_at
            ];
        })->values();
        
        // Loại bỏ các trường con khỏi jobFieldValues để tối ưu dữ liệu trả về
        // Thu thập tất cả ID của các trường con đã được xử lý
        $childFieldIds = [];
        foreach ($childFields as $parentId => $fieldGroup) {
            foreach ($fieldGroup as $childField) {
                $childFieldIds[] = $childField->id;
            }
        }
        
        // Lọc ra các trường con khỏi jobFieldValues
        if (!empty($childFieldIds)) {
            // Tạo một collection mới chỉ chứa các trường cha (không có các trường con)
            $filteredFieldValues = $job->jobFieldValues->reject(function($value) use ($childFieldIds) {
                return in_array($value->id, $childFieldIds);
            });
            
            // Gán lại collection đã lọc vào jobFieldValues
            $job->setRelation('jobFieldValues', $filteredFieldValues);
        }
        
        // Thiết lập thông tin quyền hạn sử dụng Policy
        $permissions = [
            'can_view' => true, // Đã kiểm tra ở trên
            'can_edit' => Gate::forUser($user)->allows('update', $job),
            'can_delete' => Gate::forUser($user)->allows('delete', $job),
            'can_approve' => Gate::forUser($user)->allows('approve', $job),
            'can_follow' => Gate::forUser($user)->allows('toggleFollow', $job),
            'role' => $this->determineUserRole(
                $job->isManager($user), 
                $job->isFollower($user), 
                $job->isCreator($user), 
                $job->isAssignee($user)
            )
        ];
        
        // Gán tất cả quyền trong một lần duy nhất
        $job->permissions = $permissions;
        
        // Đính kèm thông tin chi tiết nếu có quyền phê duyệt
        if ($permissions['can_approve']) {
            // Trả về tất cả các stage mà user có thể phê duyệt
            $job->pending_approval = $job->getAllApprovalStagesForUser($user);
        }
        
        // Xác định trạng thái hiển thị
        $job->display_status = $this->determineJobDisplayStatus($job, $permissions);
        
        return $job;
    }
    
    /**
     * Xử lý các jobApprovalHistories để lấy thông tin người dùng từ approved_list và followers
     * 
     * @param \Illuminate\Support\Collection $jobApprovalHistories Danh sách các lịch sử phê duyệt
     * @param \Illuminate\Support\Collection $allActions Danh sách tất cả các hành động
     * @param \Illuminate\Support\Collection $allStages Danh sách tất cả các giai đoạn
     * @return void
     */
    protected function processJobApprovalHistories($jobApprovalHistories, $allActions, $allStages)
    {
        if ($jobApprovalHistories) {
            foreach ($jobApprovalHistories as $history) {
                // Xử lý approved_list
                if (isset($history->approved_list) && !empty($history->approved_list)) {
                    $approvedList = $history->approved_list;
                    // Không cần json_decode vì model đã sử dụng $casts
                    
                    if (is_array($approvedList) && !empty($approvedList)) {
                        // Kiểm tra cấu trúc mảng để đảm bảo có thể trích xuất user_id
                        $approverIds = [];
                        foreach ($approvedList as $approver) {
                            if (is_array($approver) && isset($approver['user_id'])) {
                                $approverIds[] = $approver['user_id'];
                            } elseif (is_string($approver)) {
                                $approverIds[] = $approver;
                            }
                        }
                        
                        if (!empty($approverIds)) {
                            $approvers = User::whereIn('id', $approverIds)->get()->keyBy('id');
                            
                            // Cập nhật thông tin người dùng vào mảng approved_list
                            $approvedListWithUsers = [];
                            foreach ($approvedList as $index => $approver) {
                                $userId = is_array($approver) ? ($approver['user_id'] ?? null) : $approver;
                                if ($userId && isset($approvers[$userId])) {
                                    if (is_array($approver)) {
                                        $approver['user_info'] = $approvers[$userId];
                                        $approvedListWithUsers[] = $approver;
                                    } else {
                                        $approvedListWithUsers[] = [
                                            'user_id' => $userId,
                                            'user_info' => $approvers[$userId]
                                        ];
                                    }
                                }
                            }
                            $history->approved_list_users = $approvedListWithUsers;
                        }
                    }
                }
                
                // Xử lý followers
                if (isset($history->followers) && !empty($history->followers)) {
                    $historyFollowers = $history->followers;
                    // Không cần json_decode vì model đã sử dụng $casts
                    
                    if (is_array($historyFollowers) && !empty($historyFollowers)) {
                        // Kiểm tra cấu trúc mảng để đảm bảo có thể trích xuất user_id
                        $historyFollowerIds = [];
                        foreach ($historyFollowers as $follower) {
                            if (is_array($follower) && isset($follower['user_id'])) {
                                $historyFollowerIds[] = $follower['user_id'];
                            } elseif (is_string($follower)) {
                                $historyFollowerIds[] = $follower;
                            }
                        }
                        
                        if (!empty($historyFollowerIds)) {
                            $historyFollowerUsers = User::whereIn('id', $historyFollowerIds)->get()->keyBy('id');
                            
                            // Cập nhật thông tin người dùng vào mảng followers
                            $followersWithUsers = [];
                            foreach ($historyFollowers as $index => $follower) {
                                $userId = is_array($follower) ? ($follower['user_id'] ?? null) : $follower;
                                if ($userId && isset($historyFollowerUsers[$userId])) {
                                    if (is_array($follower)) {
                                        $follower['user_info'] = $historyFollowerUsers[$userId];
                                        $followersWithUsers[] = $follower;
                                    } else {
                                        $followersWithUsers[] = [
                                            'user_id' => $userId,
                                            'user_info' => $historyFollowerUsers[$userId]
                                        ];
                                    }
                                }
                            }
                            $history->follower_users = $followersWithUsers;
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Xử lý dữ liệu từ sub_column_table và thêm vào objectData
     * 
     * @param array &$objectData Dữ liệu của đối tượng cần cập nhật
     * @param mixed $relatedObject Đối tượng liên kết
     * @param array $subColumnTable Cấu hình sub_column_table
     * @param string $objectTable Tên bảng đối tượng từ field
     * @return void
     */
    protected function processSubColumnTable(&$objectData, $relatedObject, $subColumnTable, $objectTable = null)
    {
        if (is_array($subColumnTable)) {
            // Lấy mô tả cột từ tệp ngôn ngữ nếu có object_table
            $columnDescriptions = [];
            if ($objectTable) {
                $columnDescriptions = __('columns.' . $objectTable);
            }
            
            // Xử lý sub_column_table là mảng, thêm từng trường theo key tương ứng
            foreach ($subColumnTable as $subKey => $subField) {
                // Lấy giá trị từ đối tượng liên kết
                $subFieldValue = $relatedObject->{$subField} ?? null;
                
                // Xác định tên hiển thị cho trường
                $displayKey = $subKey;
                if (is_array($columnDescriptions) && isset($columnDescriptions[$subField])) {
                    // Sử dụng mô tả từ tệp ngôn ngữ nếu có
                    $displayKey = $columnDescriptions[$subField];
                }
                
                // Gán giá trị vào đối tượng dữ liệu với key là mô tả từ tệp ngôn ngữ hoặc subKey
                $objectData[$displayKey] = $subFieldValue;
            }
        }
    }
    
    /**
     * Thực hiện phê duyệt công việc
     */
    public function approveJob($approvalHistoryId, $userId, $data)
    {
        $approvalHistory = $this->jobApprovalHistoryRepository->find($approvalHistoryId);
        
        if (!$approvalHistory) {
            return [
                'success' => false,
                'message' => 'Không tìm thấy lịch sử phê duyệt.'
            ];
        }
        
        // Lấy công việc và người dùng
        $job = $approvalHistory->job;
        $user = User::find($userId);
        
        if (!$job || !$user) {
            return [
                'success' => false,
                'message' => 'Không tìm thấy thông tin công việc hoặc người dùng.'
            ];
        }
        
        // Kiểm tra quyền phê duyệt sử dụng Policy
        if (!Gate::forUser($user)->allows('approve', $job)) {
            return [
                'success' => false,
                'message' => 'Bạn không có quyền phê duyệt công việc này.'
            ];
        }
        
        // Kiểm tra công việc đã được phê duyệt chưa
        if ($approvalHistory->user_id) {
            return [
                'success' => false,
                'message' => 'Công việc này đã được phê duyệt.'
            ];
        }
        
        // Cập nhật thông tin phê duyệt
        $updateData = [
            'user_id' => $userId,
            'action_id' => $data['action_id'],
            'comment' => $data['comment'] ?? null,
            'date' => now()
        ];
        
        $this->jobApprovalHistoryRepository->update($updateData, $approvalHistoryId);
        
        return [
            'success' => true,
            'message' => 'Phê duyệt công việc thành công.',
            'data' => $this->jobApprovalHistoryRepository->find($approvalHistoryId)
        ];
    }

    /**
     * Xử lý một trường dữ liệu và trích xuất giá trị từ các đối tượng liên kết
     * 
     * @param JobFieldValue $fieldValue Trường dữ liệu cần xử lý
     * @param array $availableModels Danh sách các model có sẵn
     * @param bool $isVietnamese Ngôn ngữ hiện tại của người dùng
     * @return void
     */
    protected function processFieldValue($fieldValue, $availableModels, $isVietnamese)
    {
        // Đảm bảo load field cho fieldValue
        if (!$fieldValue->relationLoaded('field')) {
            $fieldValue->load('field');
        }

        // Thêm thuộc tính label dựa trên ngôn ngữ hiện tại
        if ($fieldValue->field) {
            $fieldValue->label = $isVietnamese ? 
                ($fieldValue->field->display_name ?? $fieldValue->field->keyword) : 
                ($fieldValue->field->display_name_en ?? $fieldValue->field->display_name ?? $fieldValue->field->keyword);
        } else {
            $fieldValue->label = $fieldValue->field_id; // Fallback nếu không có field
        }
            
        // Tạo mảng để lưu trữ tất cả giá trị được trích xuất cho field này
        $extractedValues = [];
            
        // Lấy column_table và sub_column_table từ field (nếu có)
        $columnTable = $fieldValue->field->column_table ?? null;
        $subColumnTable = $fieldValue->field->sub_column_table ?? null;
            
        // Kiểm tra và thiết lập giá trị mặc định cho column_table dựa vào loại field
        if (!$columnTable && isset($fieldValue->field->type)) {
            // Thiết lập column_table mặc định dựa vào loại field
            switch ($fieldValue->field->type) {
                case 'USER':
                    $columnTable = 'full_name';
                    break;
                case 'DEPARTMENT':
                    $columnTable = 'name';
                    break;
            }
        }
    
        // Kiểm tra nếu field_value là mảng và có cấu trúc phức tạp (mảng đa chiều)
        $hasNestedStructure = false;
        if (is_array($fieldValue->field_value)) {
            foreach ($fieldValue->field_value as $value) {
                if (is_array($value)) {
                    $hasNestedStructure = true;
                    break;
                }
            }
        }
            
        // Nhóm các valueObjects theo object_type để tối ưu hóa việc load dữ liệu
        $objectsByType = $fieldValue->valueObjects->groupBy('object_type');
    
        // Xử lý theo loại cấu trúc dữ liệu
        if ($hasNestedStructure) {
            // Đây là cách xử lý cho mảng đa chiều
            $nestedValues = [];
            
            foreach ($objectsByType as $objectType => $objects) {
                // Kiểm tra xem object_type có tồn tại trong danh sách model đã đăng ký không
                if (in_array($objectType, $availableModels) || array_key_exists($objectType, $availableModels)) {
                    // Lấy tất cả ID của các đối tượng cùng loại
                    $objectIds = $objects->pluck('object_id')->toArray();
                    
                    try {
                        // Tạo instance của model tương ứng
                        $modelInstance = app($objectType);
                        
                        // Lấy dữ liệu của các đối tượng liên kết
                        $relatedObjects = $modelInstance->whereIn('id', $objectIds)->get()->keyBy('id');
                        
                        // Xử lý trích xuất dữ liệu từ relatedObjects dựa trên cấu trúc phức tạp
                        foreach ($objects as $valueObject) {
                            if (isset($relatedObjects[$valueObject->object_id])) {
                                $relatedObject = $relatedObjects[$valueObject->object_id];
                                
                                // Xử lý column_table dựa trên định dạng
                                if (is_array($columnTable)) {
                                    // Nếu column_table là mảng
                                    $objectData = [];
                                    
                                    foreach ($columnTable as $key => $fieldName) {
                                        $objectData[$key] = $relatedObject->{$fieldName} ?? null;
                                    }
                                    
                                    // Xử lý sub_column_table nếu có và là mảng
                                    if ($subColumnTable && is_array($subColumnTable)) {
                                        $this->processSubColumnTable($objectData, $relatedObject, $subColumnTable, $fieldValue->field->object_table ?? null);
                                    }
                                    
                                    // Gán dữ liệu vào cấu trúc lồng nhau
                                    $parentIndex = array_search($valueObject->object_id, $fieldValue->field_value);
                                    if ($parentIndex !== false) {
                                        $nestedValues[$parentIndex] = $objectData;
                                    } else {
                                        // Tìm trong cấu trúc lồng nhau
                                        foreach ($fieldValue->field_value as $index => $valueArray) {
                                            if (is_array($valueArray) && in_array($valueObject->object_id, $valueArray)) {
                                                if (!isset($nestedValues[$index])) {
                                                    $nestedValues[$index] = [];
                                                }
                                                $nestedValues[$index][] = $objectData;
                                            }
                                        }
                                    }
                                } else if (is_string($columnTable)) {
                                    // Nếu column_table là tên trường đơn
                                    $value = $relatedObject->{$columnTable} ?? null;
                                    
                                    // Xử lý sub_column_table cho trường đơn nếu là mảng
                                    if ($subColumnTable && is_array($subColumnTable)) {
                                        // Tạo object để chứa giá trị chính và các giá trị phụ
                                        $dataObject = ['main' => $value];
                                        
                                        // Lấy mô tả cột từ tệp ngôn ngữ
                                        $columnDescriptions = [];
                                        if ($fieldValue->field && $fieldValue->field->object_table) {
                                            $columnDescriptions = __('columns.' . $fieldValue->field->object_table);
                                        }
                                        
                                        // Thêm các giá trị từ sub_column_table
                                        foreach ($subColumnTable as $subKey => $subField) {
                                            // Lấy giá trị từ đối tượng liên kết
                                            $subFieldValue = $relatedObject->{$subField} ?? null;
                                            
                                            // Xác định tên hiển thị cho trường
                                            $displayKey = $subKey;
                                            
                                            // Sử dụng mô tả từ tệp ngôn ngữ nếu có
                                            if (is_array($columnDescriptions) && isset($columnDescriptions[$subField])) {
                                                $displayKey = $columnDescriptions[$subField];
                                            }
                                            
                                            // Gán giá trị với key là mô tả hoặc subKey
                                            $dataObject[$displayKey] = $subFieldValue;
                                        }
                                        
                                        $value = $dataObject;
                                    }
                                    
                                    // Gán dữ liệu vào cấu trúc lồng nhau
                                    $parentIndex = array_search($valueObject->object_id, $fieldValue->field_value);
                                    if ($parentIndex !== false) {
                                        $nestedValues[$parentIndex] = $value;
                                    } else {
                                        // Tìm trong cấu trúc lồng nhau
                                        foreach ($fieldValue->field_value as $index => $valueArray) {
                                            if (is_array($valueArray) && in_array($valueObject->object_id, $valueArray)) {
                                                if (!isset($nestedValues[$index])) {
                                                    $nestedValues[$index] = [];
                                                }
                                                $nestedValues[$index][] = $value;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        // Log lỗi nếu không thể khởi tạo model hoặc truy vấn dữ liệu
                        \Illuminate\Support\Facades\Log::error("Lỗi khi lấy dữ liệu cho model {$objectType} với cấu trúc lồng nhau: " . $e->getMessage());
                    }
                }
            }
            
            // Gán giá trị lồng nhau đã xử lý
            $extractedValues = $nestedValues;
        } else {
            // Xử lý các trường hợp thông thường
            foreach ($objectsByType as $objectType => $objects) {
                // Kiểm tra xem object_type có tồn tại trong danh sách model đã đăng ký không
                if (in_array($objectType, $availableModels) || array_key_exists($objectType, $availableModels)) {
                    // Lấy tất cả ID của các đối tượng cùng loại
                    $objectIds = $objects->pluck('object_id')->toArray();
                    
                    try {
                        // Tạo instance của model tương ứng
                        $modelInstance = app($objectType);
                        
                        // Lấy dữ liệu của các đối tượng liên kết
                        $relatedObjects = $modelInstance->whereIn('id', $objectIds)->get()->keyBy('id');
                        
                        // Xử lý trích xuất dữ liệu từ relatedObjects dựa trên column_table
                        foreach ($objects as $valueObject) {
                            if (isset($relatedObjects[$valueObject->object_id]) && $columnTable) {
                                $relatedObject = $relatedObjects[$valueObject->object_id];
                                
                                // Xử lý column_table dựa trên định dạng
                                if (is_array($columnTable)) {
                                    // Nếu column_table là mảng
                                    $objectData = [];
                                    
                                    foreach ($columnTable as $key => $fieldName) {
                                        $objectData[$key] = $relatedObject->{$fieldName} ?? null;
                                    }
                                    
                                    // Xử lý sub_column_table nếu có và là mảng
                                    if ($subColumnTable && is_array($subColumnTable)) {
                                        $this->processSubColumnTable($objectData, $relatedObject, $subColumnTable, $fieldValue->field->object_table ?? null);
                                    }
                                    
                                    $extractedValues[] = $objectData;
                                } else if (is_string($columnTable)) {
                                    // Nếu column_table là tên trường đơn
                                    $value = $relatedObject->{$columnTable} ?? null;
                                    
                                    // Xử lý sub_column_table cho trường đơn nếu là mảng
                                    if ($subColumnTable && is_array($subColumnTable)) {
                                        // Tạo object để chứa giá trị chính và các giá trị phụ
                                        $dataObject = ['main' => $value];
                                        
                                        // Lấy mô tả cột từ tệp ngôn ngữ
                                        $columnDescriptions = [];
                                        if ($fieldValue->field && $fieldValue->field->object_table) {
                                            $columnDescriptions = __('columns.' . $fieldValue->field->object_table);
                                        }
                                        
                                        // Thêm các giá trị từ sub_column_table
                                        foreach ($subColumnTable as $subKey => $subField) {
                                            // Lấy giá trị từ đối tượng liên kết
                                            $subFieldValue = $relatedObject->{$subField} ?? null;
                                            
                                            // Xác định tên hiển thị cho trường
                                            $displayKey = $subKey;
                                            
                                            // Sử dụng mô tả từ tệp ngôn ngữ nếu có
                                            if (is_array($columnDescriptions) && isset($columnDescriptions[$subField])) {
                                                $displayKey = $columnDescriptions[$subField];
                                            }
                                            
                                            // Gán giá trị với key là mô tả hoặc subKey
                                            $dataObject[$displayKey] = $subFieldValue;
                                        }
                                        
                                        $value = $dataObject;
                                    }
                                    
                                    $extractedValues[] = $value;
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        // Log lỗi nếu không thể khởi tạo model hoặc truy vấn dữ liệu
                        \Illuminate\Support\Facades\Log::error("Lỗi khi lấy dữ liệu cho model {$objectType}: " . $e->getMessage());
                    }
                } else {
                    // Log thông báo nếu object_type không tồn tại trong danh sách model
                    \Illuminate\Support\Facades\Log::warning("Model không được đăng ký: {$objectType}");
                }
            }
        }
            
        // Gán tất cả giá trị đã trích xuất vào fieldValue
        $fieldValue->extracted_values = $extractedValues;
            
        // Loại bỏ các đối tượng valueObjects không cần thiết để giảm dung lượng
        $fieldValue->unsetRelation('valueObjects');
    }

    /**
     * Lấy tên giai đoạn đặc biệt dựa trên ID
     *
     * @param string|int $stageId ID của giai đoạn
     * @param string|null $defaultName Tên mặc định nếu không phải là giai đoạn đặc biệt
     * @return string Tên giai đoạn đặc biệt hoặc tên mặc định
     */
    protected function getSpecialStageName($stageId, $defaultName = null)
    {
        if (is_string($stageId)) {
            $specialStages = [
                'start' => 'Bắt đầu',
                'done' => 'Hoàn thành',
                'false' => 'Thất bại',
                'cancel' => 'Hủy bỏ',
                'rejected' => 'Từ chối',
            ];
            
            // Kiểm tra nếu stageId là một trong các giai đoạn đặc biệt
            if (array_key_exists(strtolower($stageId), $specialStages)) {
                return $specialStages[strtolower($stageId)];
            }
            
            // Nếu không có tên mặc định nhưng là string, trả về chính nó (viết hoa chữ cái đầu)
            if ($defaultName === null) {
                return ucfirst($stageId);
            }
        }
        
        return $defaultName;
    }

    /**
     * Lấy tên hành động đặc biệt dựa trên ID
     *
     * @param string|int $actionId ID của hành động
     * @param string|null $defaultName Tên mặc định nếu không phải là hành động đặc biệt
     * @return string Tên hành động đặc biệt hoặc tên mặc định
     */
    protected function getSpecialActionName($actionId, $defaultName = null)
    {
        if (is_string($actionId)) {
            $specialActions = [
                'create' => 'Tạo mới',
                'back_to' => 'Quay lại',
                'done' => 'Hoàn thành',
                'cancel' => 'Hủy bỏ',
                'reject' => 'Từ chối',
                'approve' => 'Phê duyệt',
                'submit' => 'Gửi đi',
            ];
            
            // Kiểm tra nếu actionId là một trong các hành động đặc biệt
            if (array_key_exists(strtolower($actionId), $specialActions)) {
                return $specialActions[strtolower($actionId)];
            }
            
            // Nếu không có tên mặc định nhưng là string, trả về chính nó (viết hoa chữ cái đầu)
            if ($defaultName === null) {
                return ucfirst($actionId);
            }
        }
        
        return $defaultName;
    }
}