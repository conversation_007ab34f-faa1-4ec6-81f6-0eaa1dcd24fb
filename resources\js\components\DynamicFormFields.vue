<template>
	<div>
		<FormKit
			ref="dynamicForm"
			type="form"
			:actions="false"
			incomplete-message=" "
			@submit="handleSubmitForm"
		>
			<Form ref="form" @submit="handleSubmitForm" :validation-schema="schema(sortedFormFields)">
				<div class="mb-3" v-for="(fieldItem, index) in sortedFormFields" :key="index">
					<CCol :xs="fieldItem.column_width">
						<div v-if="fieldItem.type === 'MULTISELECT'">
							<label class="mb-1">
								{{ fieldItem.label }}
								<span v-if="fieldItem.validation" class="text-danger">*</span>
							</label>
							<Field 
								:name="fieldItem.name"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="fieldItem.multiple ? 'tags' : 'single'"
									:value="fieldItem.value"
									v-model="state.formData[fieldItem.name]"
									:placeholder="fieldItem.placeholder"
									:close-on-select="false"
									:searchable="true"
									:disabled="fieldItem.disabled"
									:options="fieldItem.options"
									:can-clear="fieldItem.validation ? false : true"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="fieldItem.name"
								class="text-danger"
							/>
						</div>
						<div v-else-if="fieldItem.type === 'USER'">
							<label class="mb-1">
								{{ fieldItem.label }}
								<span v-if="fieldItem.validation" class="text-danger">*</span>
							</label>
							<Field 
								:name="fieldItem.name"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="fieldItem.multiple ? 'tags' : 'single'"
									:value="fieldItem.value"
									v-model="state.formData[fieldItem.name]"
									:placeholder="fieldItem.placeholder"
									:close-on-select="false"
									:filter-results="false"
									:resolve-on-load="false"
									:infinite="true"
									:limit="10"
									:clear-on-search="true"
									:searchable="true"
									:delay="0"
									:min-chars="0"
									:object="true"
									:disabled="fieldItem.disabled"
									:options="async (query) => {
										return await debouncedGetOptionUsers(query)
									}"
									:can-clear="fieldItem.validation ? false : true"
									@open="debouncedGetOptionUsers('')"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="fieldItem.name"
								class="text-danger"
							/>
						</div>
						<div v-else-if="fieldItem.type === 'DEPARTMENT'">
							<label class="mb-1">
								{{ fieldItem.label }}
								<span v-if="fieldItem.validation" class="text-danger">*</span>
							</label>
							<Field 
								:name="fieldItem.name"
								v-slot="{ field }"
							>
								<Multiselect
									v-bind="field"
									:mode="fieldItem.multiple ? 'tags' : 'single'"
									:value="fieldItem.value"
									v-model="state.formData[fieldItem.name]"
									:placeholder="fieldItem.placeholder"
									:close-on-select="false"
									:searchable="true"
									:object="true"
									:disabled="fieldItem.disabled"
									:options="state.selectOptionDepartments"
									:can-clear="fieldItem.validation ? false : true"
								>
									<template v-slot:option="{ option }">
                                        <div class="custom-option">
                                            <div class="option-label mb-1">
                                                {{ option.label }}
                                            </div>
                                            <div class="option-description text-secondary">
                                                <small>
                                                    <i>{{ option.type }}</i>
                                                </small>
                                            </div>
                                        </div>
                                    </template>
                                </Multiselect>
							</Field>
							<ErrorMessage
								as="div"
								:name="fieldItem.name"
								class="text-danger"
							/>
						</div>
						<div v-else-if="fieldItem.type === 'FILEUPLOAD'">
							<label class="mb-1">
								{{ fieldItem.label }}
								<span v-if="fieldItem.validation" class="text-danger">*</span>
							</label>
							<Field 
								:name="fieldItem.name"
								v-model="state.formData[fieldItem.name]"
							>
								<FilePond
									:files="state.formData[fieldItem.name]"
									@updatefiles="(fileItemUploads) => updateFiles(fileItemUploads, fieldItem.name)"
									className="file-pond"
									:labelIdle="$t('validate_field.file_upload.label_idle')"
									:allowMultiple="fieldItem.multiple"
									:maxFiles="state.maxFiles"
									:maxFileSize="state.maxFileSize"
									:acceptedFileTypes="state.acceptedFileTypes"
									:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
									:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
									:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
									:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
									:instantUpload="false"
									:name="fieldItem.name"
									:ref="fieldItem.name"
									credits="false"
									allow-reorder="true"
									item-insert-location="after"
									image-preview-min-height="60"
									image-preview-max-height="60"
								/>
							</Field>
							<ErrorMessage
								as="div"
								:name="fieldItem.name"
								class="text-danger"
							/>
						</div>
						<div v-else-if="fieldItem.type === 'FORMULA'">
							<FormKit
								type="text"
								:label="fieldItem.label"
								:floating-label="fieldItem.floatingLabel"
								:name="fieldItem.name"
								v-model="formattedFormulaResults[fieldItem.name]"
								disabled="false"
								label-class="required-label"
							/>
						</div>
						<div v-else-if="fieldItem.type === 'TABLE'">
							<label class="mb-1">
								{{ fieldItem.label }}
							</label>
							<table class="table table-borderless table-job-add">
								<thead>
									<tr>
										<th v-for="(field_children, fieldIndex) in converFormFields(fieldItem.childrens, true)" :key="fieldIndex">
											{{ field_children.label }}
											<span v-if="field_children.validation" class="text-danger">*</span>
										</th>
									</tr>
								</thead>
								<tbody>
									<tr v-for="(itemChildren, itemIndex) in formattedFormulaChildrenResults(fieldItem.name)" :key="itemIndex">
										<td
											v-for="(field_children, fieldIndex) in converFormFields(fieldItem.childrens, true)"
											:key="fieldIndex"
											:class="['col', `col-md-${field_children.column_width}`, `column-width-td`]"
										>
											<div v-if="field_children.type === 'MULTISELECT'">
												<Field
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													v-slot="{ field }"
												>
													<Multiselect
														v-bind="field"
														:mode="field_children.multiple ? 'tags' : 'single'"
														v-model="itemChildren[field_children.name]"
														:placeholder="field_children.placeholder"
														:close-on-select="false"
														:searchable="true"
														:disabled="field_children.disabled"
														:options="field_children.options"
														:can-clear="field_children.validation ? false : true"
													/>
												</Field>
												<ErrorMessage
													as="div"
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													class="text-danger"
												/>
											</div>
											<div v-else-if="field_children.type === 'USER'">
												<Field
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													v-slot="{ field }"
												>
													<Multiselect
														v-bind="field"
														:mode="field_children.multiple ? 'tags' : 'single'"
														v-model="itemChildren[field_children.name]"
														:placeholder="field_children.placeholder"
														:close-on-select="false"
														:filter-results="false"
														:resolve-on-load="false"
														:infinite="true"
														:limit="10"
														:clear-on-search="true"
														:searchable="true"
														:delay="0"
														:min-chars="0"
														:object="true"
														:disabled="field_children.disabled"
														:options="async (query) => {
															return await debouncedGetOptionUsers(query)
														}"
														:can-clear="field_children.validation ? false : true"
														@open="debouncedGetOptionUsers('')"
													/>
												</Field>
												<ErrorMessage
													as="div"
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													class="text-danger"
												/>
											</div>
											<div v-else-if="field_children.type === 'DEPARTMENT'">
												<Field
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													v-slot="{ field }"
												>
													<Multiselect
														v-bind="field"
														:mode="field_children.multiple ? 'tags' : 'single'"
														v-model="itemChildren[field_children.name]"
														:placeholder="field_children.placeholder"
														:close-on-select="false"
														:searchable="true"
														:object="true"
														:disabled="field_children.disabled"
														:options="state.selectOptionDepartments"
														:can-clear="field_children.validation ? false : true"
													>
														<template v-slot:option="{ option }">
															<div class="custom-option">
																<div class="option-label mb-1">
																	{{ option.label }}
																</div>
																<div class="option-description text-secondary">
																	<small>
																		<i>{{ option.type }}</i>
																	</small>
																</div>
															</div>
														</template>
													</Multiselect>
												</Field>
												<ErrorMessage
													as="div"
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													class="text-danger"
												/>
											</div>
											<div v-else-if="field_children.type === 'FILEUPLOAD'">
												<Field
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													v-model="itemChildren[field_children.name]"
												>
													<FilePond
														:files="itemChildren[field_children.name]"
														@updatefiles="(fileItemUploads) => updateFileChildrens(fileItemUploads, itemChildren, field_children.name)"
														className="file-pond-children"
														:labelIdle="$t('validate_field.file_upload.label_idle_children')"
														:allowMultiple="field_children.multiple"
														:maxFiles="state.maxFiles"
														:maxFileSize="state.maxFileSize"
														:acceptedFileTypes="state.acceptedFileTypes"
														:labelFileTypeNotAllowed="$t('validate_field.file_upload.label_allowed')"
														:labelMaxFileSizeExceeded="$t('validate_field.file_upload.label_max_file_size_exceeded')"
														:fileValidateTypeLabelExpectedTypes="`${$t('validate_field.file_upload.label_expected_types')}`"
														:labelMaxFileSize="`${$t('validate_field.file_upload.label_max_file_size')} {filesize}`"
														:instantUpload="false"
														:name="`itemChildrens[${itemIndex}].${field_children.name}`"
														:ref="`itemChildrens[${itemIndex}].${field_children.name}`"
														credits="false"
														allow-reorder="true"
														item-insert-location="after"
														image-preview-min-height="60"
														image-preview-max-height="60"
													/>
												</Field>
												<ErrorMessage
													as="div"
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													class="text-danger"
												/>
											</div>
											<div v-else-if="field_children.type === 'FORMULA'">
												<FormKit
													type="text"
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													v-model="itemChildren[field_children.name]"
													disabled="false"
												/>
											</div>
											<div v-else>
												<FormKit
													:type="field_children.type"
													:name="`itemChildrens[${itemIndex}].${field_children.name}`"
													:validation="field_children.validation"
													:validation-messages="field_children.validationMessages"
													:class="field_children.class"
													:placeholder="field_children.placeholder"
													v-model="itemChildren[field_children.name]"
													:disabled="field_children.disabled"
												/>
											</div>
										</td>
										<td class="col-md-1">
											<svg
												xmlns="http://www.w3.org/2000/svg"
												height="24px"
												viewBox="0 -960 960 960"
												width="24px"
												fill="#83868C"
												class="cursor-pointer mt-2"
												@click="removeItem(fieldItem, itemIndex)"
												v-if="itemIndex !== 0"
											>
												<path d="m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z"/>
											</svg>
										</td>
									</tr>
								</tbody>
							</table>
							<button
								type="button"
								class="btn btn-primary ms-2 d-flex align-items-center"
								@click="addItem(fieldItem)"
							>
								<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF">
									<path d="M440-280h80v-160h160v-80H520v-160h-80v160H280v80h160v160Zm40 200q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/>
								</svg>
							</button>
						</div>
						<div v-else>
							<FormKit
								v-bind="fieldItem"
								v-model="state.formData[fieldItem.name]"
							/>
						</div>
					</CCol>
				</div>
			</Form>
		</FormKit>
	</div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, computed, onMounted, PropType } from 'vue'
import { useI18n } from "vue-i18n";
import Multiselect from '@vueform/multiselect';
import vueFilePond from 'vue-filepond';
import 'filepond/dist/filepond.min.css';
import 'filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css';
import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import { numberCommas } from "@/utils/utils";
import { evaluate } from 'mathjs';

import useForms from '@/composables/form';
import useOptions from '@/composables/option';
import debounce from 'lodash.debounce';
import { Form, Field, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import { WORKFLOWS } from "@/constants/constants";

const FilePond: any = vueFilePond(FilePondPluginImagePreview, FilePondPluginFileValidateType, FilePondPluginFileValidateSize);

export default defineComponent({
	name: "DynamicFormFields",
	
	components: {
		Multiselect,
		FilePond,
		Form,
		Field,
		ErrorMessage,
	},

	props: {
		formId: {
			type: [String, Number],
			required: false
		},
		initialFormData: {
			type: Object as PropType<any>,
			default: () => ({})
		}
	},

	emits: ['submit', 'update:formData'],

	setup(props, { emit }) {
		const { t } = useI18n();
		const dynamicForm: any = ref(null);

		const state = reactive({
			maxFiles: 50,
			maxFileSize: '5MB',
			acceptedFileTypes: [
				'image/*',
				'application/pdf', 
				'application/msword', 
				'application/vnd.ms-excel', 
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'text/plain' 
			],
			formData: {} as any,
			itemChildrens: [] as Array<any>,
			dataFormFields: [] as Array<any>,
			selectOptionDepartments: [] as Array<any>,
			subColumnTableDescription: {} as { [key: string]: any },
			subColumnTableOptionSelected: {} as { [key: string]: any },
			subColumnTableDescriptionChildren: {} as { [key: string]: { [index: number]: any } },
			subColumnTableOptionSelectedChildren: {} as { [key: string]: { [index: number]: any } },
			selectOptionSystemDefaults: [
				{ label: `${t('workflow.option_system_default.create_by')}`, description: `${t('workflow.option_system_default.create_by_desc')}`, value: WORKFLOWS.OPTION_SYSTEM_DEFAULT.CREATE_BY_ID },
			] as Array<any>,
		});

		const sortedFormFields = computed(() => {
			return converFormFields(state.dataFormFields, false);
		});

		const transformObject = (obj: object) => {
			const result: any = {};
			Object.entries(obj).forEach(([key, value]) => {
				if (value === 1) result[key] = true;
				else if (value === 0) result[key] = false;
				else if (typeof value === 'string' && isJsonArrayOrObject(value)) {
					result[key] = JSON.parse(value);
				} else {
					result[key] = value;
				}
			});
			return result;
		};

		const isJsonArrayOrObject = (str: any) => {
			try {
				const parsed = JSON.parse(str);
				return typeof parsed === 'object' && parsed !== null;
			} catch (e) {
				return false;
			}
		};

		const mappedTypeDataField = (typeField: string) => {
			switch (typeField) {
				case 'VARCHAR':
					return 'text';
				case 'TEXT':
					return 'textarea';
				case 'INTEGER':
					return 'number';
				case 'FLOAT':
					return 'number';
				case 'DATE':
					return 'date';
				case 'TIME':
					return 'time';
				case 'SELECT':
					return 'MULTISELECT';
				case 'RADIO':
					return 'radio';
				case 'CHECKLIST':
					return 'checkbox';
				default:
					return typeField;
			}
		}

		const converFormFields = (dataFormFields: any, fieldTable: boolean) => {
			if (dataFormFields.length > 0) {
				const sortedDataFields = dataFormFields.filter((item: any) => fieldTable ? item.parent_id !== null : item.parent_id === null).slice().sort((a: any, b: any) => a.order - b.order);
				const transformedData = sortedDataFields.map((dataField: any) => {
					const currentLanguage: string = localStorage.getItem('app_language') || 'vi';
					const labelKey = currentLanguage === 'vi' ? 'display_name' : 'display_name_en';
					const placeholderKey = currentLanguage === 'vi' ? 'placeholder' : 'placeholder_en';
					const item = transformObject(dataField);

					let validation = item.required ? 'required' : '';
					let validationMessages = {} as any;

					const formField = {
						type: mappedTypeDataField(item.type),
						label: item[labelKey],
						placeholder: item[placeholderKey],
						floatingLabel: 'true',
						class: 'form-control',
						name: item.keyword,
						validation: validation,
						validationMessages: validationMessages,
						column_width: item.column_width,
					} as any;

					if (item.required) {
						if (item.type === 'RADIO' || item.type === 'CHECKLIST') {
							formField.legendClass = 'required-label';
						} else {
							formField.labelClass = 'required-label';
						}
						validationMessages.required = `${item[labelKey]} ${t('validate_field.display_name.required')}`;
					}

					if (item.default_value !== null) {
						formField.value = item.default_value;
					}

					if (item.not_edit) {
						formField.disabled = item.not_edit;
					}

					if (item.type === 'RADIO' || item.type === 'CHECKLIST' || item.type === 'DATE' || item.type === 'TIME') {
						formField.floatingLabel = 'false';
					}

					if ((item.type === 'VARCHAR' || item.type === 'TEXT') && item.min_equal != null && item.max_equal != null) {
						const min = parseInt(item.min_equal, 10);
						const max = parseInt(item.max_equal, 10);

						if (!isNaN(min) && !isNaN(max)) {
							validation += (validation ? '|' : '') + `length:${min},${max}`;
							validationMessages.length = `${item[labelKey]} ${t('validate_field.display_name.must_between_text')} ${min} ${t('validate_field.display_name.to')} ${max} ${t('validate_field.display_name.characters')}`;
						}
					}

					if (item.type === 'INTEGER' && item.min_equal != null && item.max_equal != null) {
						const min = parseInt(item.min_equal, 10);
						const max = parseInt(item.max_equal, 10);

						if (!isNaN(min) && !isNaN(max)) {
							validation += (validation ? '|' : '') + `between:${min},${max}`;
							validationMessages.between = `${item[labelKey]} ${t('validate_field.display_name.must_between_number')} ${min} ${t('validate_field.display_name.to')} ${max}`;
						}
					}

					if (validation) {
						formField.validation = validation;
					}

					Object.keys(formField).forEach(key => {
						if (!formField[key] && formField[key] !== 0) {
							delete formField[key];
						}
					});

					if ((item.type === 'RADIO' || item.type === 'CHECKLIST') && item.options) {
						formField.options = item.options;
					}

					if (item.type === 'SELECT') {
						formField.options = item.options;
						formField.multiple = item.multiple;
						formField.disabled = item.not_edit;
					}

					if (item.type === 'USER' || item.type === 'DEPARTMENT' || item.type === 'FILEUPLOAD' || item.type === 'OBJECTSYSTEM') {
						formField.multiple = item.multiple;
						formField.disabled = item.not_edit;
					}

					if (item.parent_id !== null) {
						formField.parent_id = item.parent_id;
					}

					if (item.children.length > 0) {
						formField.childrens = item.children.map((child: any) => {
							return transformObject(child);
						});
					}

					if (item.type === 'OBJECTSYSTEM') {
						if (item.column_table !== null) {
							formField.column_table = item.column_table;
						}
						if (item.object_table !== null) {
							formField.object_table = item.object_table;
						}
						if (item.sub_column_table !== null) {
							formField.sub_column_table = item.sub_column_table;
						}
					}

					return formField;
				});

				transformedData.forEach((field: any) => {
					if (field.type === 'TABLE' && field.childrens) {
						state.itemChildrens[field.name] = [{ ...initializeItem(field.childrens) }];
					}
				});

				return transformedData;
			} else {
				return [];
			}
		}

		const initializeItem = (childrens: any) => {
			const newItem = {};
			childrens.forEach((child: any) => {
				newItem[child.keyword] = child.default_value;
				if (typeof child.default_value === 'string' && child.default_value.startsWith('=')) {
					newItem['keyword_formula'] = child.keyword;
					newItem['original_formula'] = child.default_value;
				}
			});

			return newItem;
		}

		const { getUsers, getDepartments } = useOptions();
		const { getFieldsByFormId } = useForms();

		const getOptionUsers = async (query: string) => {
			let result = await getUsers(query);
			if (Array.isArray(result) && result.length > 0) {
				return result.map((elem: any) => (
					{
						value: elem.id,
						label: `${elem.account_name} - ${elem.full_name}`,
					}
				));
			}
		}

		const debouncedGetOptionUsers = debounce(getOptionUsers, 500);

		const getOptionDepartments = async () => {
			let result = await getDepartments();
			if (Array.isArray(result) && result.length > 0) {
				state.selectOptionDepartments = result.map((elem: any) => (
					{
						value: elem.id,
						label: elem.name,
						type: elem.type,
					}
				));
			}
		}

		const updateFiles = (fileItemUploads: any, fieldName: string) => {
			state.formData[fieldName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

		const updateFileChildrens = (fileItemUploads: any, itemChildren: object, fieldChildrenName: string) => {
			itemChildren[fieldChildrenName] = fileItemUploads.length > 0 ? fileItemUploads.map((fileItem : any) => fileItem.file) : null;
		};

		const calculateFormula = (formula: string, context: any) => {
			try {
				const expression = formula.substring(1);
				const result = evaluate(expression, context);
				return result;
			} catch (error) {
				console.error('Formula calculation error:', error);
				return 0;
			}
		};

		const formulaResults = computed(() => {
			const results = {};
			sortedFormFields.value.forEach((field: any) => {
				if (field.type === 'FORMULA') {
					const formula = field.value;
					if (formula && formula.startsWith('=')) {
						results[field.name] = calculateFormula(field.value, results);
					}
				}
			});

			return results;
		});

		const formattedFormulaResults = computed(() => {
			const results = {};
			for (const key in formulaResults.value) {
				results[key] = numberCommas(formulaResults.value[key]);
				state.formData[key] = results[key];
			}

			return results;
		});

		const schema = (field: any[]) => {
			let schemaForm = yup.object().shape({});

			const itemShape: { [key: string]: any } = {};

			field.forEach((item: any) => {
				if (item.type !== 'TABLE') {
					if (item.validation && item.disabled == false) {
						switch (item.type) {
							case 'FILEUPLOAD':
								const validationRule = yup.array().required(`${item.validationMessages.required}`);
								itemShape[item.name] = validationRule;
								break;
							case 'MULTISELECT':
								if (!item.value || item.value?.length === 0) {
									const validationRule = item.multiple
										? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
										: yup.string().required(`${item.validationMessages.required}`);
									itemShape[item.name] = validationRule;
								}
								break;
							case 'OBJECTSYSTEM':
							case 'USER':
							case 'DEPARTMENT':
								if (!item.value || item.value?.length === 0) {
									const validationRule = item.multiple
										? yup.array().min(1, `${item.validationMessages.required}`).required(`${item.validationMessages.required}`)
										: yup.object().required(`${item.validationMessages.required}`);
									itemShape[item.name] = validationRule;
								}
								break;
							default:
								itemShape[item.name] = yup.mixed();
						}
					}
				}
			});

			schemaForm = schemaForm.shape({
				itemChildrens: yup.array().of(
					yup.lazy((itemChildren: any) => {
						const itemChildrenShape: { [key: string]: any } = {};

						field.forEach((item: any) => {
							if (item.type === 'TABLE') {
								const field_childrens = converFormFields(item.childrens, true);
								field_childrens.forEach((item_children: any) => {
									if (item_children.validation && item_children.disabled == false) {
										switch (item_children.type) {
											case 'FILEUPLOAD':
												const validationRule = yup.array().required(`${item_children.validationMessages.required}`);
												itemChildrenShape[item_children.name] = validationRule;
												break;
											case 'MULTISELECT':
												if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
													const validationRule = item_children.multiple
														? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
														: yup.string().required(`${item_children.validationMessages.required}`);
													itemChildrenShape[item_children.name] = validationRule;
												}
												break;
											case 'OBJECTSYSTEM':
											case 'USER':
											case 'DEPARTMENT':
												if (!item_children.value || itemChildren[item_children.name]?.length === 0) {
													const validationRule = item_children.multiple
														? yup.array().min(1, `${item_children.validationMessages.required}`).required(`${item_children.validationMessages.required}`)
														: yup.object().required(`${item_children.validationMessages.required}`);
													itemChildrenShape[item_children.name] = validationRule;
												}
												break;
											default:
												itemChildrenShape[item_children.name] = yup.mixed();
										}
									}
								});
							}
						});

						return yup.object().shape(itemChildrenShape);
					})
				),
				...itemShape
			});

			return schemaForm;
		};

		const handleSubmitForm = async () => {
			emit('submit', {
				formData: state.formData,
				itemChildrens: state.itemChildrens,
				dataFormFields: state.dataFormFields
			});
		}

		// Load form fields when formId changes
		const loadFormFields = async () => {
			if (props.formId) {
				try {
					const result = await getFieldsByFormId(props.formId.toString(), '1'); // Assuming stageId = 1 as default
					if (result && Array.isArray(result)) {
						state.dataFormFields = result;
						await initializeValues();
					}
				} catch (error) {
					console.error('Error loading form fields:', error);
				}
			}
		};

		const initializeValues = async () => {
			for (const field of sortedFormFields.value) {
				if (field.value) {
					state.formData[field.name] = field.value;
				}
			}
		};

		// Initialize departments
		onMounted(async () => {
			await getOptionDepartments();
			await loadFormFields();
		});

		const addItem = (field: any) => {
			state.itemChildrens[field.name].push({ ...initializeItem(field.childrens) });
		};

		const removeItem = (field: any, itemIndex: number) => {
			state.itemChildrens[field.name].splice(itemIndex, 1);
		};

		const formattedFormulaChildrenResults = (nameKey: string) => {
			const items = state.itemChildrens[nameKey];
			if (!items) return [];
			items.forEach((item: any) => {
				if (item.original_formula) {
					const calculatedValue = calculateFormula(item.original_formula, item);
					item[item.keyword_formula] = numberCommas(calculatedValue);
				}
			});

			return items;
		};

		return {
			state,
			dynamicForm,
			sortedFormFields,
			converFormFields,
			formattedFormulaResults,
			formattedFormulaChildrenResults,
			schema,
			debouncedGetOptionUsers,
			updateFiles,
			updateFileChildrens,
			addItem,
			removeItem,
			handleSubmitForm,
		}
	}
});
</script>

<style scoped>
.cursor-pointer {
	cursor: pointer;
}
.column-width-td {
	min-width: 200px !important;
}
</style>
<style src="@vueform/multiselect/themes/default.css"></style>
