<template>
    <div class="d-flex justify-content-start paginate">
        <div class="d-flex justify-content-start align-items-center">
            <span class="fw-normal">{{ $t('paginate.per_page') }} {{ meta.from ? meta.from : 0 }} - {{ meta.to ? meta.to : 0 }} / {{ meta.total }} {{ $t('paginate.record') }}</span>
            <span class="ms-3 fw-normal">{{ $t('paginate.page') }}: <span>{{ meta.currentPage < 10 ? '0' + meta.currentPage : meta.currentPage }}</span></span>
            <b-dropdown size="lg" variant="link" toggle-class="text-decoration-none" no-caret class="dropdown-menu-page">
                <template #button-content>
                    <span class="material-icons-outlined">expand_more</span>
                </template>
                <b-dropdown-item 
                    class="text-center"
                    disabled
                >
                    <span>{{ $t('paginate.page') }}</span>
                </b-dropdown-item>
                <b-dropdown-divider></b-dropdown-divider>
                <b-dropdown-item 
                    v-for="page in meta.lastPage"
                    :key="page"
                    @click.prevent="getResults(page)"
                    class="text-center"
                >
                    <span v-if="page !== meta.currentPage">{{ page < 10 ? '0' + page : page }}</span>
                    <span class="active-page" v-else>{{ page < 10 ? '0' + page : page }}</span>
                </b-dropdown-item>
            </b-dropdown>
            <span class="fw-normal">/ {{ meta.lastPage < 10 ? '0' + meta.lastPage : meta.lastPage }}</span>
            <button 
                class="nav-link mt-2 ms-3"
                :class="{ 'disabled': links.prev === null }"
                @click.prevent="links.prev && getResults(meta.currentPage - currentPage)"
            >
                <span class="material-icons-outlined">navigate_before</span>
            </button>
            <button 
                class="nav-link mt-2 ms-1"
                :class="{ 'disabled': links.next === null }"
                @click.prevent="links.next && getResults(meta.currentPage + currentPage)"
            >
                <span class="material-icons-outlined">navigate_next</span>
            </button>
            <div class="ms-3">
                <select 
                    class="form-select" 
                    v-model="perPage"
                    @change="showPerPage()"
                >
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
            </div>
        </div>
        
    </div>
    
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';

export default defineComponent({
    name: 'Paginate',

    props: {
        meta: {
            type: Object,
            required: true
        },
        links: {
            type: Object,
            required: true
        },
    },

    emits: ['page', 'per-page'],

    setup(props, {emit}) {
        const currentPage = ref(1);
        const perPage = ref(10);

        const getResults = (page: number) => {
            emit('page', page);
        };

        const showPerPage = () => {
            emit('per-page', perPage.value);
        };
        

        return {
            currentPage,
            perPage,
            getResults,
            showPerPage
        }
    },
});
</script>

<style scoped>

</style>