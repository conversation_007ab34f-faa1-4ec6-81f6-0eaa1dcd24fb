<?php

// Khai báo namespace tương ứng với vị trí file
namespace App\Enums;

use Illuminate\Support\Str;

// Định nghĩa Enum với kiểu giá trị nền là string
enum ProcessStageStatus: string
{
    // Đ<PERSON>nh nghĩa các trường hợp (cases) có thể có và giá trị chuỗi tương ứng
    case PROCESSING = 'processing'; // Đang xử lý
    case COMPLETED = 'completed'; // Hoàn thành
    case PENDING = 'pending'; // Chờ
    case CANCEL = 'cancel'; // Hủy

    public function label(): string
    {
        // 1. Xác định tiền tố (tên file ngôn ngữ)
        $file = 'enums';

        // 2. Tạo phần giữa của key từ tên lớp Enum (WorkflowStatus -> workflow_status)
        $enum_key = Str::snake(class_basename(static::class));

        // 3. L<PERSON>y phần cuối của key từ giá trị của case hiện tại
        $case_value = $this->value;

        // 4. Ghép lại thành key đầy đủ
        $translation_key = "{$file}.{$enum_key}.{$case_value}";

        // 5. Trả về bản dịch bằng hàm helper __()
        return __($translation_key);
    }
}