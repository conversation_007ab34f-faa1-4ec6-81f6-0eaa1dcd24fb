<template>
    WorkflowDetail
    <loading :isLoading="setIsLoading" />
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import Loading from '@/views/loading/Loading.vue'

export default defineComponent({
    name: 'WorkflowDetail',

    components: {
        Loading,
    },

    setup() {
        const setIsLoading = ref(false);

        return {
            setIsLoading,
        };
    },
});
</script>
