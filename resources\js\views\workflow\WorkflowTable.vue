<template>
    <CTable align="middle" responsive>
        <table class="table table-hover">
            <thead>
                <tr>
                    <th class="align-middle">
                        {{ $t('workflow.name') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.status') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.process_version') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.process_group') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.created_by') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.created_at') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.updated_at') }}
                    </th>
                    <th class="align-middle">
                        {{ $t('workflow.updated_by') }}
                    </th>
                </tr>
            </thead>
            <tbody v-if="checkDataNotEmpty">
                <tr v-for="(workflow, index) in dataWorkflows" :key="index">
                    <td class="align-middle">
                        <router-link :to="{ name: 'WorkflowDetail', params: { id: workflow.id } }" class="text-decoration-none">
                            {{ workflow?.name || $t('common.no_data') }}
                        </router-link>
                    </td>
                    <td class="align-middle">
                        <span class="badge rounded-pill bg-danger" v-if="workflow.status == WORKFLOWS.STATUS.UNACTIVE">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_workflow.unactive') }}
                            </small>
                        </span>
                        <span class="badge rounded-pill bg-success" v-else-if="workflow.status == WORKFLOWS.STATUS.ACTIVE">
                            <small class="fst-normal text-white">
                                {{ $t('option_tab_workflow.active') }}
                            </small>
                        </span>
                    </td>
                    <td class="align-middle">
                        <span class="text-dark fw-bold" v-if="workflow.status == WORKFLOWS.STATUS.SAVE_DRAFT">
                            {{ $t('option_tab_workflow.save_draft') }}
                        </span>
                        <span class="text-success fw-bold" v-else>
                            {{ $t('workflow.use_real') }}
                        </span>
                    </td>
                    <td class="align-middle">
                        {{ workflow?.process_group?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.created_by?.name || $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.created_at ? formatDate(workflow?.created_at) : $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.updated_at ? formatDate(workflow?.updated_at) : $t('common.no_data') }}
                    </td>
                    <td class="align-middle">
                        {{ workflow?.updated_by?.name || $t('common.no_data') }}
                    </td>
                </tr>
            </tbody>
            <tbody v-else>
                <tr>
                    <td colspan="10" class="align-middle text-center">
                        {{ $t('search.no_matching_records_found') }}
                    </td>
                </tr>
            </tbody>
        </table>
    </CTable>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue'
import { WORKFLOWS } from '@/constants/constants';
import moment from 'moment';

export default defineComponent({
    name: 'WorkflowTable',
    emits: ['update-data-paginate'],

    props: {
        dataWorkflows: {
            type: Array as () => Array<any>, 
            required: true,
            default: () => []
        },
    },

    setup(props: any, {emit}) {
        const checkDataNotEmpty = computed<boolean>(() => {
            return props.dataWorkflows.length > 0;
        });

        const formatDate = (date: string) => {
            return moment(date).format('DD/MM/YYYY');
        };

        return {
            WORKFLOWS,
            checkDataNotEmpty,
            formatDate,
        }
    },
});
</script>
<style type="text/css" scoped>
</style>