<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\SaveJob;
use App\Models\ProcessVersion;
use App\Models\Process;
use App\Models\Form;
use App\Models\Field;
use App\Models\Stage;
use App\Models\Action;
use App\Models\StageTransition;
use App\Enums\SaveJobStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;

class SaveJobExecuteActionTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $saveJob;
    protected $processVersion;
    protected $stage;
    protected $action;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo user test
        $this->user = User::factory()->create();
        
        // Tạo form và fields
        $form = Form::factory()->create();
        $field = Field::factory()->create(['form_id' => $form->id]);
        
        // Tạo process và process version
        $process = Process::factory()->create(['form_id' => $form->id]);
        $this->processVersion = ProcessVersion::factory()->create(['process_id' => $process->id]);
        
        // Tạo stage và action
        $this->stage = Stage::factory()->create(['process_version_id' => $this->processVersion->id]);
        $this->action = Action::factory()->create(['process_version_id' => $this->processVersion->id]);
        
        // Tạo save job
        $this->saveJob = SaveJob::factory()->create([
            'user_id' => $this->user->id,
            'process_version_id' => $this->processVersion->id,
            'status' => SaveJobStatus::PENDING->value
        ]);
    }

    /** @test */
    public function test_execute_action_with_missing_stage_id_returns_error()
    {
        $this->actingAs($this->user);
        
        $response = $this->postJson("/api/jobs/{$this->saveJob->id}/execute-action", [
            'action_id' => $this->action->id,
            'comment' => 'Test comment'
        ]);
        
        $response->assertStatus(400)
                ->assertJson([
                    'status' => false,
                    'message' => 'Thiếu thông tin stage_id hoặc action_id.'
                ]);
    }

    /** @test */
    public function test_execute_action_with_missing_action_id_returns_error()
    {
        $this->actingAs($this->user);
        
        $response = $this->postJson("/api/jobs/{$this->saveJob->id}/execute-action", [
            'stage_id' => $this->stage->id,
            'comment' => 'Test comment'
        ]);
        
        $response->assertStatus(400)
                ->assertJson([
                    'status' => false,
                    'message' => 'Thiếu thông tin stage_id hoặc action_id.'
                ]);
    }

    /** @test */
    public function test_execute_action_with_invalid_job_id_returns_error()
    {
        $this->actingAs($this->user);
        
        $response = $this->postJson("/api/jobs/invalid-job-id/execute-action", [
            'stage_id' => $this->stage->id,
            'action_id' => $this->action->id,
            'comment' => 'Test comment'
        ]);
        
        $response->assertStatus(404)
                ->assertJson([
                    'status' => false,
                    'message' => 'Không tìm thấy công việc.'
                ]);
    }

    /** @test */
    public function test_execute_action_logs_important_information()
    {
        $this->actingAs($this->user);
        
        // Mock Log facade để kiểm tra log
        Log::shouldReceive('info')
           ->with('Starting executeAction', \Mockery::type('array'))
           ->once();
           
        Log::shouldReceive('warning')
           ->with('No stage transitions found', \Mockery::type('array'))
           ->once();
           
        Log::shouldReceive('warning')
           ->with('No data processed in actionProcess', \Mockery::type('array'))
           ->once();
           
        Log::shouldReceive('error')
           ->with('ActionProcess returned false', \Mockery::type('array'))
           ->once();
        
        $response = $this->postJson("/api/jobs/{$this->saveJob->id}/execute-action", [
            'stage_id' => $this->stage->id,
            'action_id' => $this->action->id,
            'comment' => 'Test comment'
        ]);
        
        // Kiểm tra response
        $response->assertStatus(422)
                ->assertJson([
                    'status' => false,
                    'error_code' => 'ACTION_PROCESS_FAILED'
                ]);
    }

    /** @test */
    public function test_execute_action_with_valid_stage_transition()
    {
        $this->actingAs($this->user);
        
        // Tạo stage transition để có dữ liệu xử lý
        $nextStage = Stage::factory()->create(['process_version_id' => $this->processVersion->id]);
        
        StageTransition::factory()->create([
            'process_version_id' => $this->processVersion->id,
            'from_stage_id' => $this->stage->id,
            'to_stage_id' => $nextStage->id,
            'action_id' => $this->action->id
        ]);
        
        // Mock Log để kiểm tra log thành công
        Log::shouldReceive('info')
           ->with('Starting executeAction', \Mockery::type('array'))
           ->once();
           
        Log::shouldReceive('info')
           ->with('ExecuteAction completed successfully', \Mockery::type('array'))
           ->once();
        
        $response = $this->postJson("/api/jobs/{$this->saveJob->id}/execute-action", [
            'stage_id' => $this->stage->id,
            'action_id' => $this->action->id,
            'comment' => 'Test comment'
        ]);
        
        // Kiểm tra response thành công
        $response->assertStatus(200)
                ->assertJson([
                    'status' => 'success',
                    'message' => 'Thực hiện hành động thành công.'
                ]);
    }
}