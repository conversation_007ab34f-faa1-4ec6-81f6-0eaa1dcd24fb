// vite.config.ts
import { defineConfig } from "file:///C:/xampp/htdocs/tvnas-app/node_modules/vite/dist/node/index.js";
import laravel from "file:///C:/xampp/htdocs/tvnas-app/node_modules/laravel-vite-plugin/dist/index.js";
import vue from "file:///C:/xampp/htdocs/tvnas-app/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import autoprefixer from "file:///C:/xampp/htdocs/tvnas-app/node_modules/autoprefixer/lib/autoprefixer.js";
import Components from "file:///C:/xampp/htdocs/tvnas-app/node_modules/unplugin-vue-components/dist/vite.js";
import { BootstrapVueNextResolver } from "file:///C:/xampp/htdocs/tvnas-app/node_modules/bootstrap-vue-next/dist/bootstrap-vue-next.mjs";
var __vite_injected_original_dirname = "C:\\xampp\\htdocs\\tvnas-app";
var vite_config_default = defineConfig(() => {
  return {
    plugins: [
      laravel({
        input: [
          "resources/css/app.css",
          "resources/js/app.ts"
        ],
        refresh: true
      }),
      vue({
        template: {
          transformAssetUrls: {
            base: null,
            includeAbsolute: false
          }
        }
      }),
      Components({
        resolvers: [BootstrapVueNextResolver()]
      })
    ],
    css: {
      postcss: {
        plugins: [
          autoprefixer({})
          // add options if needed
        ]
      },
      preprocessorOptions: {
        scss: {
          quietDeps: true
        }
      }
    },
    resolve: {
      alias: {
        vue: "vue/dist/vue.esm-bundler.js",
        "@": path.resolve(__vite_injected_original_dirname, "resources/js")
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue", ".scss", ".png"]
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
