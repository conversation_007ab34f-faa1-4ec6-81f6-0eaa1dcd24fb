import axios from 'axios';
import { ref, reactive } from 'vue';
import { useToast } from 'vue-toast-notification';
import { useRouter } from 'vue-router';
import { useI18n } from "vue-i18n";

export default function useFields() {
    const $toast = useToast();
    const router = useRouter();
    const { t }  = useI18n();

    const setIsLoading = ref(false);

    const catchError = async (error: any) => {
        const status = error?.response?.status;
        if (!status) {
            console.error(error);
            return;
        }
        switch (status) {
            case 422:
            case 404:
            case 500:
                $toast.open({
                    message: error.response.data.message,
                    type: "error",
                    duration: 5000,
                    dismissible: true,
                    position: "bottom-right",
                });
                break;
            default:
                console.log(error);
                break;
        }
    }

    const getUsers = async (valueQuery: string) => {
        try {
            let response = await axios.get('/api/option-users', {
				params: {
					query: valueQuery,
				}
			});

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getUsers(valueQuery);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getDepartments = async () => {
        try {
            let response = await axios.get('/api/option-departments');

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getDepartments();
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getJobPositionSystems = async () => {
        try {
            let response = await axios.get('/api/option-job-position-systems');

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getJobPositionSystems();
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getRankSystems = async () => {
        try {
            let response = await axios.get('/api/option-rank-systems');

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getRankSystems();
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getWorkflows = async (valueQuery: string) => {
        try {
            let response = await axios.get('/api/option-workflows', {
				params: {
					query: valueQuery,
				}
			});

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getWorkflows(valueQuery);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getProcessGroups = async (valueQuery: string) => {
        try {
            let response = await axios.get('/api/option-process-groups', {
				params: {
					query: valueQuery,
				}
			});

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getProcessGroups(valueQuery);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getScopes = async (valueQuery: string) => {
        try {
            let response = await axios.get('/api/option-scopes', {
				params: {
					query: valueQuery,
				}
			});

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getScopes(valueQuery);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    const getUserByOptionScopes = async (optionScopes: any, workflowId: string) => {
        try {
            let response = await axios.get('/api/user-scopes', {
				params: {
					optionScopes: optionScopes,
                    workflowId: workflowId,
				}
			});

            return response?.data;
        } catch (error: any) {
            if (!error.response) {
                setTimeout(() => {
                    getUserByOptionScopes(optionScopes, workflowId);
                }, 1000);

                return;
            }

            catchError(error);
        }
    }

    return {
        setIsLoading,
        getUsers,
        getDepartments,
        getJobPositionSystems,
        getRankSystems,
        getWorkflows,
        getProcessGroups,
        getScopes,
        getUserByOptionScopes
    }
}